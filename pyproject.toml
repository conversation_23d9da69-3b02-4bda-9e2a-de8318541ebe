[tool.poetry]
name = "cuju-ai-inference-server"
version = "0.0.1"
description = "Cuju AI Inference Server"
authors = [
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<PERSON>@mail.schwarz>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<PERSON>.<PERSON>@inovex.de>",
    "<PERSON><PERSON><PERSON> <<PERSON><PERSON><PERSON>.<PERSON>@mail.schwarz>",
    "<PERSON> <<PERSON>@inovex.de>",
    "<PERSON><PERSON><PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>"
]
readme = "README.md"
license = "tbd"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12,<3.13"
ray = { extras = ["serve"], version = "*" }
boto3 = { version = "*" }
tomli = "^2.2.1"
jinja2 = "^3.1.6"

[tool.poetry.group.dev.dependencies]
ruff = "^0.11.3"

[build-system]
requires = ["poetry-core>=1.2.0"]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "cuju-ai-workbench"
url = "https://cuju-ai-651706744076.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple"
priority = "explicit"


[tool.ruff]
line-length = 88
indent-width = 4
target-version = "py39"
extend-exclude = ["bundled/libs"]
exclude = ["thirdparty", "dev", ".venv", "ci"]

[tool.ruff.lint]
select = [
    "F",
    "E",
    "W",
    "C",
    "I",
    "N",
    "D",
    "UP",
    "ANN",
    "ASYNC",
    "ASYNC1",
    "S",
    "BLE",
    "FBT",
    "B",
    "A",
    "COM",
    "C4",
    "DTZ",
    "T10",
    "EM",
    "ISC",
    "ICN",
    "LOG",
    "G",
    "INP",
    "PIE",
    "T20",
    "PYI",
    "PT",
    "Q",
    "RSE",
    "RET",
    "SLF",
    "SLOT",
    "SIM",
    "TID",
    "TCH",
    "INT",
    "ARG",
    "PTH",
    "TD",
    "FIX",
    "ERA",
    "PL",
    "TRY",
    "FLY",
    "PERF",
    "FURB",
    "RUF",
]

ignore = [
    "C901",    # complex structure
    "PTH122",  # os-path-splitext
    "SIM115",  # open-file-with-context-handler
    "ANN401",  # any-type
    "PERF203", # try-except-in-loop
    "PERF401", # manual-list-comprehension
    "PLW0603", # global-statement
    "COM812",  # missing-trailing-comma (conflicts when used with the formatter)
]
fixable = ["ALL"]
unfixable = []

[tool.ruff.lint.per-file-ignores]
"**/tests_*/**/*.py" = [
    "PLR2004", # magic-value-comparison
    "S101",    # assert
]
"**/test_*/**/*.py" = [
    "PLR2004", # magic-value-comparison
    "S101",    # assert
]

[tool.ruff.lint.pydocstyle]
convention = "numpy"

[tool.ruff.lint.isort]
order-by-type = true
relative-imports-order = "closest-to-furthest"
extra-standard-library = ["typing"]
section-order = [
    "future",
    "standard-library",
    "third-party",
    "first-party",
    "local-folder",
]
known-first-party = []

[tool.ruff.format]
docstring-code-format = true
