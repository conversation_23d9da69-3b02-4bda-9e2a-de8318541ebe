definitions:
  variables: &shared_variables
    - name: Environment
      default: AI-Integration
      allowed-values:
        - AI-Integration
        - CUJU-AI-Integration
        - CUJU-AI-Test
        - CUJU-AI-Production
  services:
    docker:
      memory: 3072

  steps:
    - step: &get_aws_shared_account_credentials
        name: Get AWS Shared Account credentials
        image: python:3.12
        script:
          - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
          - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
          - pip install awscli
          - aws codeartifact login --tool pip --domain cuju-ai --domain-owner ************ --repository cuju-ai-workbench --region eu-central-1
          - aws codeartifact get-authorization-token --domain cuju-ai --domain-owner ************ --region eu-central-1 --query authorizationToken --output text > code-artifact-token.txt
        artifacts:
          - code-artifact-token.txt
    - step: &get_environment_variables
        name: Get AWS environment variables
        image: python:3.12
        script:
          - chmod +x ci/scripts/get_environment_variables.sh
          - ci/scripts/get_environment_variables.sh
        artifacts:
          - set_env.sh
          - id_rsa
    - step: &build_and_publish_docker_image_to_aws_ecr
        name: Build and Publish Docker Image to AWS ECR
        image: python:3.12
        services:
          - docker
        script:
          - pip install awscli
          - export IMAGE_TAG="${DOCKER_IMAGE_TAG:-stable}"
          - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
          - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
          - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ************.dkr.ecr.eu-central-1.amazonaws.com
          - >
            python ci/scripts/update_dependencies/build_docker_image.py
            --image-name cuju-ai-workbench
            --dockerfile-path ci/Dockerfile.cpu-aws
            --context-dir .
            --image-tag "$IMAGE_TAG"
            --aws-account-id ************
          - >
            python ci/scripts/update_dependencies/build_docker_image.py
            --image-name cuju-ai-workbench-cuda
            --dockerfile-path ci/Dockerfile.gpu-aws
            --context-dir .
            --image-tag "$IMAGE_TAG"
            --aws-account-id ************
    - step: &create_customed_ami
        name: Build AMIs
        image: python:3.12
        script:
          - apt-get update && apt-get install -y unzip curl
          - curl -fsSL https://releases.hashicorp.com/packer/1.12.0/packer_1.12.0_linux_amd64.zip -o packer.zip
          - unzip packer.zip && mv packer /usr/local/bin/ && packer version
          - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
          - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
          - export IMAGE_TAG="${DOCKER_IMAGE_TAG:-stable}"
          - pip install PyYAML
          - python3 ci/scripts/update_dependencies/build_amis_and_update_cluster_config.py --version "$IMAGE_TAG" --aws-account-id ************
    - step: &render_ray_cluster_config_files
        name: Render Ray cluster config files
        image: python:3.12
        script:
          - source ./set_env.sh
          - pip install jinja2 pyyaml

          - case "$Environment" in
              *AI-Integration)
                ENV_FOLDER="integration"
                INSTALL_DEPS="True"
                ;;
              CUJU-AI-Test)
                ENV_FOLDER="test"
                INSTALL_DEPS="True"
                ;;
              CUJU-AI-Production)
                ENV_FOLDER="production"
                INSTALL_DEPS="False"
                ;;
              *)
            esac
          - export CLUSTER_CONFIG="./configs/$ENV_FOLDER/cluster_config.yaml"
          - export APP_CONFIGS_PATH="./configs/$ENV_FOLDER/app_configs/"
          - export SERVE_CONFIGS_PATH="./configs/$ENV_FOLDER/serve_configs/"

          - >
            python ci/scripts/render_cluster_config_template.py
            --config $CLUSTER_CONFIG
            --cluster_name cluster_1
            --region $AWS_REGION
            --availability_zone ${AWS_REGION}a
            --subnet_id $SUBNET_1_ID
            --security_group_ids $RAY_CLUSTER_SG_ID
            --iam_instance_profile $IAM_RAY_CLUSTERS_ROLE_ARN
            --app_path $APP_CONFIGS_PATH
            --serve_configs_path $SERVE_CONFIGS_PATH
            --aws_config ~/.aws/
            --jumphost_ip $JUMPHOST_IP
            --ssh_key_name $SSH_KEY_NAME
            --monitoring_instance_ip $MONITORING_INSTANCE_IP
            --detailed_monitoring "True"
            --account_id $ACCOUNT_ID
            --shared_account_id $SHARED_ACCOUNT_ID
            --install_dependencies $INSTALL_DEPS
            > ray_cluster_1.yaml

          - >
            python ci/scripts/render_cluster_config_template.py
            --config $CLUSTER_CONFIG
            --cluster_name cluster_2
            --region $AWS_REGION
            --availability_zone ${AWS_REGION}b
            --subnet_id $SUBNET_2_ID
            --security_group_ids $RAY_CLUSTER_SG_ID
            --iam_instance_profile $IAM_RAY_CLUSTERS_ROLE_ARN
            --app_path $APP_CONFIGS_PATH
            --serve_configs_path $SERVE_CONFIGS_PATH
            --aws_config ~/.aws/
            --jumphost_ip $JUMPHOST_IP
            --ssh_key_name $SSH_KEY_NAME
            --monitoring_instance_ip $MONITORING_INSTANCE_IP
            --detailed_monitoring "True"
            --account_id $ACCOUNT_ID
            --shared_account_id $SHARED_ACCOUNT_ID
            --install_dependencies $INSTALL_DEPS
            > ray_cluster_2.yaml

          - >
            python ci/scripts/render_cluster_config_template.py
            --config $CLUSTER_CONFIG
            --cluster_name cluster_3
            --region $AWS_REGION
            --availability_zone ${AWS_REGION}c
            --subnet_id $SUBNET_3_ID
            --security_group_ids $RAY_CLUSTER_SG_ID
            --iam_instance_profile $IAM_RAY_CLUSTERS_ROLE_ARN
            --app_path $APP_CONFIGS_PATH
            --serve_configs_path $SERVE_CONFIGS_PATH
            --aws_config ~/.aws/
            --jumphost_ip $JUMPHOST_IP
            --ssh_key_name $SSH_KEY_NAME
            --monitoring_instance_ip $MONITORING_INSTANCE_IP
            --account_id $ACCOUNT_ID
            --shared_account_id $SHARED_ACCOUNT_ID
            --detailed_monitoring "True"
            --install_dependencies $INSTALL_DEPS
            > ray_cluster_3.yaml
        artifacts:
          - ray_cluster_1.yaml
          - ray_cluster_2.yaml
          - ray_cluster_3.yaml
    - step: &teardown_ray_cluster_1
        name: Tear down Ray Cluster 1
        image: python:3.12
        script:
          - source ./set_env.sh
          - pip install awscli ray[default] boto3
          - chmod +x ci/scripts/teardown_ray_cluster.sh
          - ci/scripts/teardown_ray_cluster.sh $Environment ray_cluster_1.yaml ray-cluster_1-head
    - step: &teardown_ray_cluster_2
        name: Tear down Ray Cluster 2
        image: python:3.12
        script:
          - source ./set_env.sh
          - pip install awscli ray[default] boto3
          - chmod +x ci/scripts/teardown_ray_cluster.sh
          - ci/scripts/teardown_ray_cluster.sh $Environment ray_cluster_2.yaml ray-cluster_2-head
    - step: &teardown_ray_cluster_3
        name: Tear down Ray Cluster 3
        image: python:3.12
        script:
          - source ./set_env.sh
          - pip install awscli ray[default] boto3
          - chmod +x ci/scripts/teardown_ray_cluster.sh
          - ci/scripts/teardown_ray_cluster.sh $Environment ray_cluster_3.yaml ray-cluster_3-head
    - step: &deploy_ray_cluster_1
        name: Deploy Ray Cluster 1
        image: python:3.12
        script:
          - source ./set_env.sh
          - chmod +x ci/scripts/deploy_ray_cluster.sh
          - ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_1.yaml
    - step: &deploy_ray_cluster_2
        name: Deploy Ray Cluster 2
        image: python:3.12
        script:
          - source ./set_env.sh
          - chmod +x ci/scripts/deploy_ray_cluster.sh
          - ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_2.yaml
    - step: &deploy_ray_cluster_3
        name: Deploy Ray Cluster 3
        image: python:3.12
        script:
          - source ./set_env.sh
          - chmod +x ci/scripts/deploy_ray_cluster.sh
          - ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_3.yaml
    - step: &deploy_application_1
        name: Deploy Application
        image: python:3.12
        script:
        - source ./set_env.sh
        - pip install awscli ray[default] boto3
        - SERVE_CONFIG_FILE="../serve_configs/ray_config_monolith.yaml"
        - SERVE_CONFIG_STATEMENT="serve deploy $SERVE_CONFIG_FILE"
        - chmod +x ci/scripts/log_deployment.sh
        - ci/scripts/log_deployment.sh cluster_1 ray_cluster_1.yaml "$SERVE_CONFIG_FILE"
        - ray exec ray_cluster_1.yaml "$SERVE_CONFIG_STATEMENT"
        - ray exec ray_cluster_1.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"
    - step: &deploy_application_2
        name: Deploy Application
        image: python:3.12
        script:
        - source ./set_env.sh
        - pip install awscli ray[default] boto3
        - SERVE_CONFIG_FILE="../serve_configs/ray_config_monolith.yaml"
        - SERVE_CONFIG_STATEMENT="serve deploy $SERVE_CONFIG_FILE"
        - chmod +x ci/scripts/log_deployment.sh
        - ci/scripts/log_deployment.sh cluster_2 ray_cluster_2.yaml "$SERVE_CONFIG_FILE"
        - ray exec ray_cluster_2.yaml "$SERVE_CONFIG_STATEMENT"
        - ray exec ray_cluster_2.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"
    - step: &deploy_application_3
        name: Deploy Application
        image: python:3.12
        script:
        - source ./set_env.sh
        - pip install awscli ray[default] boto3
        - SERVE_CONFIG_FILE="../serve_configs/ray_config_backend.yaml"
        - SERVE_CONFIG_STATEMENT="serve deploy $SERVE_CONFIG_FILE"
        - chmod +x ci/scripts/log_deployment.sh
        - ci/scripts/log_deployment.sh cluster_3 ray_cluster_3.yaml "$SERVE_CONFIG_FILE"
        - ray exec ray_cluster_3.yaml "$SERVE_CONFIG_STATEMENT"
        - ray exec ray_cluster_3.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"

pipelines:
  branches:
    main:
      - stage:
          name: Deploy Integration
          trigger: automatic
          condition:
            changesets:
              includePaths:
                - "configs/integration/**"
          steps:
            - step:
                name: Get AWS Environment Variables
                image: python:3.12
                script:
                  - export Environment="CUJU-AI-Integration"
                  - chmod +x ci/scripts/get_environment_variables.sh
                  - ci/scripts/get_environment_variables.sh
                artifacts:
                  - set_env.sh
                  - id_rsa
            - step: *get_aws_shared_account_credentials
            - step: *render_ray_cluster_config_files
            - step: *deploy_ray_cluster_1
            - step: *deploy_application_1
            - step: *deploy_ray_cluster_2
            - step: *deploy_application_2
            - step: *deploy_ray_cluster_3
            - step: *deploy_application_3
      - stage:
          name: Deploy Test
          trigger: manual
          condition:
            changesets:
              includePaths:
                - "configs/test/**"
          steps:
            - step:
                name: Get AWS Environment Variables
                image: python:3.12
                script:
                  - export Environment="CUJU-AI-Test"
                  - chmod +x ci/scripts/get_environment_variables.sh
                  - ci/scripts/get_environment_variables.sh
                artifacts:
                  - set_env.sh
                  - id_rsa
            - step: *get_aws_shared_account_credentials
            - step: *render_ray_cluster_config_files
            - step: *deploy_ray_cluster_1
            - step: *deploy_application_1
            - step: *deploy_ray_cluster_2
            - step: *deploy_application_2
            - step: *deploy_ray_cluster_3
            - step: *deploy_application_3
      - stage:
          name: Deploy Production
          trigger: manual
          condition:
            changesets:
              includePaths:
                - "configs/production/**"
          steps:
            - step:
                name: Get AWS Environment Variables
                image: python:3.12
                script:
                  - export Environment="CUJU-AI-Production"
                  - chmod +x ci/scripts/get_environment_variables.sh
                  - ci/scripts/get_environment_variables.sh
                artifacts:
                  - set_env.sh
                  - id_rsa
            - step: *get_aws_shared_account_credentials
            - step: *render_ray_cluster_config_files
            - step: *deploy_ray_cluster_1
            - step: *deploy_application_1
            - step: *deploy_ray_cluster_2
            - step: *deploy_application_2
            - step: *deploy_ray_cluster_3
            - step: *deploy_application_3

  custom:
    create_custom_ami:
      - variables:
          - name: DOCKER_IMAGE_TAG
      - step: *build_and_publish_docker_image_to_aws_ecr
      - step: *create_customed_ami

    create_ray_cluster_config_files:
      - variables: *shared_variables
      - step: *get_environment_variables
      - step: *render_ray_cluster_config_files

    tear_down_ray_clusters:
      - variables: *shared_variables
      - step: *get_environment_variables
      - step: *render_ray_cluster_config_files
      - parallel:
          - step: *teardown_ray_cluster_1
          - step: *teardown_ray_cluster_2
          - step: *teardown_ray_cluster_3

    cluster_rolling_rollout:
      - variables: *shared_variables
      - step: *get_environment_variables
      - step: *get_aws_shared_account_credentials
      - step: *render_ray_cluster_config_files
      - step: *deploy_ray_cluster_1
      - step: *deploy_application_1
      - step: *deploy_ray_cluster_2
      - step: *deploy_application_2
      - step: *deploy_ray_cluster_3
      - step: *deploy_application_3

    cluster_recreate_rollout:
      - variables: *shared_variables
      - step: *get_environment_variables
      - step: *get_aws_shared_account_credentials
      - step: *render_ray_cluster_config_files
      - parallel:
          - step: *deploy_ray_cluster_1
          - step: *deploy_ray_cluster_2
          - step: *deploy_ray_cluster_3
      - parallel:
          - step: *deploy_application_1
          - step: *deploy_application_2
          - step: *deploy_application_3

    update_project_dependencies:
      - variables:
          - name: ENVIRONMENT
            default: CUJU-AI-Integration
            allowed-values:
              - CUJU-AI-Integration
              - CUJU-AI-Test
              - CUJU-AI-Production
          - name: VERSION
      - step:
          name: Render pyproject.toml
          image: python:3.10
          script:
            - apt-get update && apt-get install -y git
            - pip install jinja2
            - python ci/scripts/update_dependencies/render_pyproject_toml.py --environment "$ENVIRONMENT" --version "$VERSION"
          artifacts:
            - configs/**/app_configs/pyproject.toml

      - step:
          name: Build Docker Image(s)
          image: python:3.12
          services:
            - docker
          script:
            - if [ "$ENVIRONMENT" != "CUJU-AI-Production" ]; then echo "Skipping docker image build"; exit 0; fi
            - export IMAGE_TAG="${VERSION//+/-}"
            - pip install awscli
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
            - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
            - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ************.dkr.ecr.eu-central-1.amazonaws.com
            - aws codeartifact login --tool pip --domain cuju-ai --domain-owner ************ --repository cuju-ai-workbench --region eu-central-1
            - export CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain cuju-ai --domain-owner ************ --region eu-central-1 --query authorizationToken --output text)
            - >
              python ci/scripts/update_dependencies/build_docker_image.py
              --image-name cuju-ai-workbench
              --dockerfile-path ci/Dockerfile.cpu-aws
              --context-dir .
              --image-tag "$IMAGE_TAG"
              --aws-account-id ************
              --environment production
              --install-deps
            - >
              python ci/scripts/update_dependencies/build_docker_image.py
              --image-name cuju-ai-workbench-cuda
              --dockerfile-path ci/Dockerfile.gpu-aws
              --context-dir .
              --image-tag "$IMAGE_TAG"
              --aws-account-id ************
              --environment production
              --install-deps
          artifacts:
            - configs/**/app_configs/pyproject.toml
      - step:
          name: Build CPU AMI and Update Cluster Config
          image: python:3.12
          script:
            - if [ "$ENVIRONMENT" != "CUJU-AI-Production" ]; then echo "Skipping AMI build"; exit 0; fi
            - apt-get update && apt-get install -y unzip curl
            - curl -fsSL https://releases.hashicorp.com/packer/1.12.0/packer_1.12.0_linux_amd64.zip -o packer.zip
            - unzip packer.zip && mv packer /usr/local/bin/ && packer version
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
            - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
            - export IMAGE_TAG="${VERSION//+/-}"
            - pip install PyYAML
            - python3 ci/scripts/update_dependencies/build_amis_and_update_cluster_config.py --environment "$ENVIRONMENT" --version "$VERSION" --aws-account-id ************ --target cpu
          artifacts:
            - configs/**/app_configs/pyproject.toml
            - configs/**/cluster_config.yaml
      - step:
          name: Build GPU AMI and Update Cluster Config
          image: python:3.12
          script:
            - if [ "$ENVIRONMENT" != "CUJU-AI-Production" ]; then echo "Skipping AMI build"; exit 0; fi
            - apt-get update && apt-get install -y unzip curl
            - curl -fsSL https://releases.hashicorp.com/packer/1.12.0/packer_1.12.0_linux_amd64.zip -o packer.zip
            - unzip packer.zip && mv packer /usr/local/bin/ && packer version
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID_CUJU_SHARED
            - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY_CUJU_SHARED
            - export IMAGE_TAG="${VERSION//+/-}"
            - pip install PyYAML
            - python3 ci/scripts/update_dependencies/build_amis_and_update_cluster_config.py --environment "$ENVIRONMENT" --version "$VERSION" --aws-account-id ************ --target gpu
          artifacts:
            - configs/**/app_configs/pyproject.toml
            - configs/**/cluster_config.yaml
      - step:
          name: Commit Changes and Raise PR
          image: python:3.12
          script:
            - apt-get update && apt-get install -y git curl
            - pip install requests PyYAML
            - >
              python ci/scripts/update_dependencies/commit_and_raise_pr.py --environment "$ENVIRONMENT" --version "$VERSION"
