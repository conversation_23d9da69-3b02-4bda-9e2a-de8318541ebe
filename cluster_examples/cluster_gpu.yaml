---
cluster_name: cluster_dev

max_workers: 21
idle_timeout_minutes: 5

provider:
  type: aws
  region: eu-central-1
  availability_zone: eu-central-1a
  cache_stopped_nodes: False
  use_internal_ips: true

docker:
  image: ************.dkr.ecr.eu-central-1.amazonaws.com/person-detection-service:stable
  container_name: "ray_container"
  pull_before_run: false
  run_options:
    - --ulimit nofile=65536:65536
    - -v /var/log/ray_logs:/tmp/ray/

auth:
  ssh_user: ubuntu
  ssh_private_key: ~/.ssh/AI-Integration-jumphost-ray-clusters-ssh-key.pem
  ssh_proxy_command: ssh -i ~/.ssh/AI-Integration-jumphost-ray-clusters-ssh-key.pem -W %h:%p ec2-user@***********

available_node_types:
  ray.head.default:
    resources: {"CPU": 0, "GPU": 0}
    node_config:
      InstanceType: t2.xlarge
      ImageId: ami-03bd52ddf3ff4b601
      KeyName: centralized-vpc-ray-jumphost-ssh-key
      Monitoring: {'Enabled': True}
      SubnetIds:
        - subnet-0ebe269cec35574a4
      SecurityGroupIds:
        - sg-0eb3d81cf0a862a4d
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: arn:aws:iam::************:instance-profile/AI-Integration-ray

  ray.worker.gpu:
    min_workers: 1
    max_workers: 10
    resources: { "GPU": 1, "CPU": 4 }
    docker:
      worker_image: ************.dkr.ecr.eu-central-1.amazonaws.com/person-detection-service-gpu:stable
      pull_before_run: false
      worker_run_options:
        - --ulimit nofile=65536:65536
        - -v /var/log/ray_logs:/tmp/ray/
        - --gpus all
        - --runtime=nvidia
        - --device /dev/nvidia0:/dev/nvidia0
        - --device /dev/nvidiactl:/dev/nvidiactl
        - --device /dev/nvidia-uvm:/dev/nvidia-uvm
        - --device /dev/nvidia-uvm-tools:/dev/nvidia-uvm-tools
    node_config:
      InstanceType: g4dn.xlarge
      ImageId: ami-0d3e13fc5fbfde42b
      KeyName: centralized-vpc-ray-jumphost-ssh-key
      Monitoring: {'Enabled': True}
      SubnetIds:
        - subnet-0ebe269cec35574a4
      SecurityGroupIds:
        - sg-0eb3d81cf0a862a4d
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: arn:aws:iam::************:instance-profile/AI-Integration-ray
    worker_setup_commands:
      - mkdir -p /home/<USER>/.cuju/document_db && wget -P /home/<USER>/.cuju/document_db https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
      - pip install -U boto3 'ray[serve]==2.42.1'
      - aws sts assume-role --role-arn arn:aws:iam::************:role/CodeArtifactCrossAccountRole --role-session-name codeartifact-access
      - aws codeartifact login --tool pip --domain cuju-ai --domain-owner ************ --repository cuju-ai-workbench --region eu-central-1
      - aws codeartifact get-authorization-token --domain cuju-ai --domain-owner ************ --region eu-central-1 --query authorizationToken --output text > .code-artifact-token
      - poetry config repositories.aws https://cuju-ai-************.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple/
      - poetry config http-basic.aws aws $(cat .code-artifact-token)
      - rm .code-artifact-token
      - poetry config virtualenvs.create false
      - poetry lock && poetry install
      - pip uninstall --yes onnxruntime
      - pip install onnxruntime-gpu --index-url "https://pypi.org/simple" --quiet

  ray.worker.cpu:
    min_workers: 1
    max_workers: 10
    resources: { "CPU": 2 }
    docker:
      worker_image: ************.dkr.ecr.eu-central-1.amazonaws.com/person-detection-service:stable
      pull_before_run: false
      worker_run_options:
        - --ulimit nofile=65536:65536
        - -v /var/log/ray_logs:/tmp/ray/
    node_config:
      InstanceType: t2.xlarge
      ImageId: ami-03bd52ddf3ff4b601
      KeyName: centralized-vpc-ray-jumphost-ssh-key
      Monitoring: {'Enabled': True}
      SubnetIds:
        - subnet-0ebe269cec35574a4
      SecurityGroupIds:
        - sg-0eb3d81cf0a862a4d
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: arn:aws:iam::************:instance-profile/AI-Integration-ray

head_node_type: ray.head.default

file_mounts:
  {
    "/app": "./configs/integration/app_configs/",
    "/serve_configs": "./configs/integration/serve_configs/",
    "/home/<USER>/.aws/": "~/.aws/",
  }

# The nvidia-container-runtime related commands are there to fix inconsistent
# availability of nvidia drivers within the containers, where the error
# "Failed to initialize NVML: Unknown Error" would be returned when nvidia-smi is called
# within the container.
# Reference: https://bobcares.com/blog/docker-failed-to-initialize-nvml-unknown-error/
initialization_commands:
  - sudo usermod -aG docker $USER
  - "[ -f /etc/nvidia-container-runtime/config.toml ] && sudo sed -i 's/^#no-cgroups = false$/no-cgroups = false/' /etc/nvidia-container-runtime/config.toml || true"
  - "[ -f /etc/nvidia-container-runtime/config.toml ] && sudo systemctl restart docker || true"
  - sleep 20
  # - cd docker-setup && chown -R ubuntu:ubuntu /home/<USER>/docker-setup && sudo -u ubuntu /usr/local/bin/docker-compose up -d
  - aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin ************.dkr.ecr.eu-central-1.amazonaws.com
  - export CLUSTER_NAME=cluster_1 && export NAME=$HOSTNAME
  - |
    sudo bash -c 'cat <<EOF > /etc/logrotate.d/ray-logs
    /var/log/ray_logs/session_*/logs/*.log /var/log/ray_logs/session_*/logs/*.err /var/log/ray_logs/session_*/logs/*.out /var/log/ray_logs/session_*/logs/old/* {
        daily
        rotate 1
        compress
        missingok
        notifempty
        copytruncate
        su ubuntu users
    }
    EOF'

setup_commands:
  - mkdir -p /home/<USER>/.cuju/document_db && wget -P /home/<USER>/.cuju/document_db https://truststore.pki.rds.amazonaws.com/global/global-bundle.pem
  - pip install -U boto3 'ray[serve]==2.42.1'
  - aws sts assume-role --role-arn arn:aws:iam::************:role/CodeArtifactCrossAccountRole --role-session-name codeartifact-access
  - aws codeartifact login --tool pip --domain cuju-ai --domain-owner ************ --repository cuju-ai-workbench --region eu-central-1
  - aws codeartifact get-authorization-token --domain cuju-ai --domain-owner ************ --region eu-central-1 --query authorizationToken --output text > .code-artifact-token
  - poetry config repositories.aws https://cuju-ai-************.d.codeartifact.eu-central-1.amazonaws.com/pypi/cuju-ai-workbench/simple/
  - poetry config http-basic.aws aws $(cat .code-artifact-token)
  - rm .code-artifact-token
  - poetry config virtualenvs.create false
  - poetry lock && poetry install

head_start_ray_commands:
  - sudo mkdir -p /etc/prometheus
  - |
    sudo bash -c 'cat <<EOF > /etc/prometheus/prometheus.yml
    global:
      scrape_interval: 10s

    remote_write:
      - url: "http://**********:9090/api/v1/write"

    # Scrape from Ray.
    scrape_configs:
    - job_name: "cluster_dev"
      file_sd_configs:
      - files:
        - "/tmp/ray/prom_metrics_service_discovery.json"
    EOF'
  - ray stop
  - ulimit -n 65536; ray start --head  --port=6379 --object-manager-port=8076 --autoscaling-config=~/ray_bootstrap_config.yaml --dashboard-host 0.0.0.0 --dashboard-port 8265 --metrics-export-port=8080
#  -RAY_REDIS_ADDRESS=clustercfg.redis-cache-for-ray-clusters.mxcyvx.euc1.cache.amazonaws.com:6379
  - nohup prometheus --config.file=/etc/prometheus/prometheus.yml --enable-feature=agent &

worker_start_ray_commands:
  - ray stop
  - ulimit -n 65536; ray start --address=$RAY_HEAD_IP:6379 --object-manager-port=8076 --metrics-export-port=8080
