---
cluster_name: cluster_training

max_workers: 5
idle_timeout_minutes: 5

provider:
    type: aws
    region: eu-central-1
    availability_zone: eu-central-1a
    cache_stopped_nodes: False

docker:
    image: rayproject/ray:nightly-py39
    container_name: "ray_container"
    pull_before_run: true
    run_options:
        - --ulimit nofile=65536:65536
        - --cap-add SYS_ADMIN
        - --device /dev/fuse
        - --mount type=bind,source=/home/<USER>/mount,target=/mountpoint,bind-propagation=shared

auth:
    ssh_user: ubuntu

available_node_types:
  ray.head.default:
    resources: {}
    node_config:
      InstanceType: t2.xlarge
      ImageId: ami-0e872aee57663ae2d
      PrivateIpAddress: *********
      SubnetIds:
        - subnet-04ad68ca482df9e8c
      SecurityGroupIds:
        - sg-06a987be602a243a0
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: arn:aws:iam::235103184542:instance-profile/AI-Integration-ray

  ray.worker.default:
    min_workers: 1
    max_workers: 5
    resources: {}
    node_config:
      InstanceType: t2.xlarge
      ImageId: ami-0e872aee57663ae2d
      SubnetIds:
        - subnet-04ad68ca482df9e8c
      SecurityGroupIds:
        - sg-06a987be602a243a0
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 140
            VolumeType: gp3
      IamInstanceProfile:
        Arn: arn:aws:iam::235103184542:instance-profile/AI-Integration-ray

head_node_type: ray.head.default


file_mounts: {
    "/app": "cuju-ai-inference-server/",
    "/home/<USER>/.aws/config": "/Users/<USER>/.aws/config",
    "/home/<USER>/.aws/credentials": "/Users/<USER>/.aws/credentials",
}


initialization_commands:
  - curl -fsSL https://get.docker.com -o get-docker.sh
  - sudo sh get-docker.sh
  - sudo usermod -aG docker $USER
  - sudo systemctl restart docker -f
  - sudo snap install aws-cli --classic
  - aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 111889947777.dkr.ecr.eu-central-1.amazonaws.com;
  - wget https://s3.amazonaws.com/mountpoint-s3-release/latest/x86_64/mount-s3.deb -P /tmp && sudo apt install -y /tmp/mount-s3.deb && rm /tmp/mount-s3.deb
  - \[ -f /etc/fuse.conf \] && sudo sed -i 's/#user_allow_other/user_allow_other/g' /etc/fuse.conf || sudo sh -c 'echo "user_allow_other" > /etc/fuse.conf'
  - mkdir -p ~/mount/s3 && mkdir -p ~/mount/cache && mount-s3 --allow-other --cache ~/mount/cache ai-bucket-137068251345 ~/mount/s3


setup_commands:
  - sudo apt-get update && sudo apt-get install -y build-essential rsync curl git


head_start_ray_commands:
  - ray stop
  - ray start --head --port=6379 --object-manager-port=8076 --dashboard-host=0.0.0.0 --autoscaling-config=~/ray_bootstrap_config.yaml --dashboard-port 8265