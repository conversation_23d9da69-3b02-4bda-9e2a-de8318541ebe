# CI/CD Pipeline Documentation


## 1.  Is there an automated CI/CD pipeline? How is it triggered (e.g., git tag, merge to main)?

Yes, there is an automated CI/CD pipeline using Bitbucket Pipelines. The pipeline is configured in `bitbucket-pipelines.yml` and supports three environments:

- **Integration Environment** (`CUJU-AI-Integration`)
- **Test Environment** (`CUJU-AI-Test`) 
- **Production Environment** (`CUJU-AI-Production`)

### Triggering mechanism:

#### Automatic Triggers for Integration Environment: 

Automatically triggered on pushes to `main` branch when changes are detected in `configs/integration/**` files

```yaml
- stage:
    name: Deploy Integration
    trigger: automatic
    condition:
      changesets:
        includePaths:
          - "configs/integration/**"
```

#### Manual Triggers for Test Environment: 
Manually triggered when changes are made to `configs/test/**` files
#### Manual Triggers for Prod Environment:
 Manually triggered when changes are made to `configs/production/**` files

```yaml
- stage:
    name: Deploy Test
    trigger: manual
    condition:
      changesets:
        includePaths:
          - "configs/test/**"
```

### Pipeline Stages
Each deployment includes the following stages:
1. **Get AWS Environment Variables**
2. **Get AWS Shared Account Credentials**
3. **Render Ray Cluster Config Files**
4. **Deploy Ray Clusters** (3 parallel clusters)
5. **Deploy Applications** (to each cluster)

## 2. Monitoring Deployment Status


#### Bitbucket Pipeline Monitoring
You monitor by watching the Bitbucket UI for a green/red status, when it shows "green" means the application is actually healthy because the check_serve_status.py script confirmed it. (from the &deploy_application_1 step at line 209)

```bash
# 1. This line issues the deployment command to the cluster
- ray exec ray_cluster_1.yaml "$SERVE_CONFIG_STATEMENT"

# 2. This line *verifies* the deployment
- ray exec ray_cluster_1.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"
```

#### Ray Serve Health Monitoring
- **Please refer to ray documentation**
## 3. Manual Re-run of Failed Deployments

### Bitbucket UI Re-runs
- **Direct Re-run**: Use Bitbucket's built-in pipeline re-run functionality
- **Selective Re-run**: Re-run specific failed steps within a pipeline
- **Branch Re-deployment**: Push new commits to trigger automatic re-runs



## 4. Deployment Artifacts Storage and Versioning

### AWS ECR (Elastic Container Registry)
**Primary artifact storage location**: `************.dkr.ecr.eu-central-1.amazonaws.com`

#### Docker Images
- **CPU Images**: `cuju-ai-workbench:{tag}`
- **GPU Images**: `cuju-ai-workbench-cuda:{tag}`

#### Versioning Strategy
```python
def build_and_push_docker_image(
        image_name,
        dockerfile_path,
        context_dir,
        image_tag,
        aws_account_id,
        environment=None,
        install_dependencies=False
):
    ecr_repo = f"{aws_account_id}.dkr.ecr.eu-central-1.amazonaws.com/{image_name}:{image_tag}"
```
### Python Wheels: Stored in AWS CodeArtifact.


```python
aws codeartifact login --tool pip --domain cuju-ai --domain-owner ************ --repository cuju-ai-workbench
  ```
## 5. Manual Deployment When CI/CD Fails



### Manual Deployment Scripts

#### 1. Cluster Connection
```bash
./ci/scripts/connect_to_cluster.sh
```


#### 2. Manual Cluster Deployment
```bash
./ci/scripts/deploy_ray_cluster.sh <ENVIRONMENT> <RAY_CLUSTER_CONFIG>
```


#### 3. Manual Cluster Teardown
```bash
./ci/scripts/teardown_ray_cluster.sh <ENVIRONMENT> <CLUSTER_YAML> <CLUSTER_TAG>
```


### Manual Deployment Process

#### Step-by-Step Manual Deployment
1. **Environment Setup**
   ```bash
   export Environment="CUJU-AI-Production"  # or desired environment
   chmod +x ci/scripts/get_environment_variables.sh
   ./ci/scripts/get_environment_variables.sh
   source ./set_env.sh
   ```

2. **Generate Cluster Configurations**
   ```bash
   pip install jinja2 pyyaml
   python ci/scripts/render_cluster_config_template.py
   ```

3. **Deploy Ray Clusters**
   ```bash
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_1.yaml
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_2.yaml
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_3.yaml
   ```

4. **Deploy Applications**
   ```bash
   pip install awscli ray[default] boto3
   SERVE_CONFIG_FILE="../serve_configs/ray_config_monolith.yaml"
   ray exec ray_cluster_1.yaml "serve deploy $SERVE_CONFIG_FILE"
   ray exec ray_cluster_1.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"
   ```

5. **Verify Deployment**
   ```bash
   # Connect to cluster for monitoring
   ./ci/scripts/connect_to_cluster.sh
   # Access Ray Dashboard at http://localhost:8265
   # Access Grafana at http://localhost:3000
   ```
