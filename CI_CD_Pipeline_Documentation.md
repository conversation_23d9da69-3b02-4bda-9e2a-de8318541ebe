# CI/CD Pipeline Documentation


## 1.  Is there an automated CI/CD pipeline? How is it triggered (e.g., git tag, merge to main)?

Yes, there is an automated CI/CD pipeline using Bitbucket Pipelines. The pipeline is configured in `bitbucket-pipelines.yml` and supports three environments:

- **Integration Environment** (`CUJU-AI-Integration`)
- **Test Environment** (`CUJU-AI-Test`) 
- **Production Environment** (`CUJU-AI-Production`)

### Triggering mechanism:

#### Automatic Triggers for Integration Environment: 

Automatically triggered on pushes to `main` branch when changes are detected in `configs/integration/**` files

```yaml
- stage:
    name: Deploy Integration
    trigger: automatic
    condition:
      changesets:
        includePaths:
          - "configs/integration/**"
```

#### Automatic Triggers for Test Environment: 
: Manually triggered when changes are made to `configs/test/**` files
#### Automatic Triggers for Prod Environment:
 Manually triggered when changes are made to `configs/production/**` files

```yaml
- stage:
    name: Deploy Test
    trigger: manual
    condition:
      changesets:
        includePaths:
          - "configs/test/**"
```

### Pipeline Stages
Each deployment includes the following stages:
1. **Get AWS Environment Variables**
2. **Get AWS Shared Account Credentials**
3. **Render Ray Cluster Config Files**
4. **Deploy Ray Clusters** (3 parallel clusters)
5. **Deploy Applications** (to each cluster)

## 2. Monitoring Deployment Status

### Multiple Monitoring Mechanisms

#### Bitbucket Pipeline Monitoring
- **Pipeline URL Tracking**: Each deployment logs its Bitbucket pipeline URL for easy access
- **Build Number Tracking**: Uses `$BITBUCKET_BUILD_NUMBER` for unique identification
- **Real-time Pipeline Status**: Monitor progress directly in Bitbucket UI

#### Database Logging
All deployments are automatically logged to a MongoDB database with comprehensive information:

```bash
ray exec "$RAY_CLUSTER_CONFIG" "ray job submit \
    --working-dir /home/<USER>/ray_jobs \
    -- python log_deployment_to_db.py \
    --cluster_name \"$RAY_CLUSTER_NAME\" \
    --ai_inference_commit \"$BITBUCKET_COMMIT\" \
    --bitbucket_pipeline_url \"$BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/$BITBUCKET_BUILD_NUMBER\" \
    --branch \"$BITBUCKET_BRANCH\" \
    --tag \"$TAG\" \
    --pull_request_id \"$PULL_REQUEST_ID\" \
    --deployed_serve_config_file \"$RAY_SERVE_CONFIG\" \
    "
```

#### Ray Serve Health Monitoring
- **Automated Health Checks**: Pipeline includes verification that all deployments are "HEALTHY"
- **Timeout Monitoring**: 6000-second timeout with 30-second intervals
- **Ray Dashboard Access**: Available via port forwarding for real-time monitoring

```python
def wait_until_all_deployments_ready(timeout=6000, interval=30):
    ray.init(address="auto")
    serve.start(detached=True)
    start = time.time()
    while time.time() - start < timeout:
        status = serve.status()
        deployments = status.applications["E2EMonolith"].deployments
        all_running = all(d.status == "HEALTHY" for d in deployments.values())
        if all_running:
            logger.success("All deployments are running.")
            return
        time.sleep(interval)
    logger.error("Timeout: Not all deployments are running.")
    sys.exit(1)
```

#### Monitoring Access
- **Ray Dashboard**: Accessible via SSH tunnel on port 8265
- **Grafana Monitoring**: Available on port 3000
- **Prometheus Metrics**: Available on port 3333
- **Database Access**: MongoDB accessible on port 27017

## 3. Manual Re-run of Failed Deployments

### Bitbucket UI Re-runs
- **Direct Re-run**: Use Bitbucket's built-in pipeline re-run functionality
- **Selective Re-run**: Re-run specific failed steps within a pipeline
- **Branch Re-deployment**: Push new commits to trigger automatic re-runs

### Custom Pipeline Commands
The pipeline defines several custom commands for manual execution:

#### Available Custom Commands
```yaml
custom:
  create_custom_ami:
    - variables:
        - name: DOCKER_IMAGE_TAG
    - step: *build_and_publish_docker_image_to_aws_ecr
    - step: *create_customed_ami

  create_ray_cluster_config_files:
    - variables: *shared_variables
    - step: *get_environment_variables
    - step: *render_ray_cluster_config_files

  tear_down_ray_clusters:
    - variables: *shared_variables
    - step: *get_environment_variables
    - step: *render_ray_cluster_config_files
    - parallel:
        - step: *teardown_ray_cluster_1
        - step: *teardown_ray_cluster_2
        - step: *teardown_ray_cluster_3
```

### Manual Execution Steps
1. **Navigate to Bitbucket Repository**
2. **Go to Pipelines Section**
3. **Select "Run Pipeline"**
4. **Choose Custom Pipeline**
5. **Select Appropriate Command**
6. **Set Required Variables**
7. **Execute Pipeline**

## 4. Deployment Artifacts Storage and Versioning

### AWS ECR (Elastic Container Registry)
**Primary artifact storage location**: `************.dkr.ecr.eu-central-1.amazonaws.com`

#### Docker Images
- **CPU Images**: `cuju-ai-workbench:{tag}`
- **GPU Images**: `cuju-ai-workbench-cuda:{tag}`

#### Versioning Strategy
```python
def build_and_push_docker_image(
        image_name,
        dockerfile_path,
        context_dir,
        image_tag,
        aws_account_id,
        environment=None,
        install_dependencies=False
):
    ecr_repo = f"{aws_account_id}.dkr.ecr.eu-central-1.amazonaws.com/{image_name}:{image_tag}"
```

#### Tag Management
- **Default Tag**: "stable" (when `DOCKER_IMAGE_TAG` not specified)
- **Custom Tags**: Set via `DOCKER_IMAGE_TAG` variable
- **Version Normalization**: Special characters replaced (e.g., `+` becomes `-`)

### AWS AMI Storage
- **Custom AMIs**: Built using Packer and stored in AWS
- **Naming Convention**: `ray-cpu-fluentbit-{image_tag}` and `ray-gpu-fluentbit-{image_tag}`
- **Sharing**: AMIs shared across specified AWS account IDs

### Configuration Artifacts
- **Pipeline Artifacts**: Stored as Bitbucket pipeline artifacts
- **Generated Configs**: `configs/**/app_configs/pyproject.toml` and `configs/**/cluster_config.yaml`
- **Version Control**: Changes committed back to repository via automated PRs

### CodeArtifact Integration
- **Private Package Repository**: `cuju-ai-workbench` in CodeArtifact
- **Authentication**: Token-based authentication for private packages
- **Domain**: `cuju-ai` (Domain Owner: ************)

## 5. Manual Deployment When CI/CD Fails

### Direct Cluster Access
Use the connection script to access clusters directly:

```bash
#!/bin/bash
options=("CUJU-AI-Integration" "CUJU-AI-Test" "CUJU-AI-Production")
echo "Please select an environment:"
PS3=$'\n  \e[33mEnter selection:\e[0m '
select opt in "${options[@]}"; do
    case $REPLY in
        1) environment="CUJU-AI-Integration"; break ;;
        2) environment="CUJU-AI-Test"; break ;;
        3) environment="CUJU-AI-Production"; break ;;
        *) echo "Invalid option. Please try again." ;;
    esac
done
```

### Manual Deployment Scripts

#### 1. Cluster Connection
```bash
./ci/scripts/connect_to_cluster.sh
```
**Features:**
- Interactive environment selection
- Automatic SSH key retrieval from AWS SSM
- Port forwarding setup for monitoring tools
- DocDB connection tunneling

#### 2. Manual Cluster Deployment
```bash
./ci/scripts/deploy_ray_cluster.sh <ENVIRONMENT> <RAY_CLUSTER_CONFIG>
```
**Process:**
- Sets up AWS credentials
- Installs required dependencies
- Deploys Ray cluster using specified configuration
- Configures networking and security

#### 3. Manual Cluster Teardown
```bash
./ci/scripts/teardown_ray_cluster.sh <ENVIRONMENT> <CLUSTER_YAML> <CLUSTER_TAG>
```
**Features:**
- Graceful cluster shutdown
- Instance state monitoring
- Cleanup verification

### Manual Deployment Process

#### Step-by-Step Manual Deployment
1. **Environment Setup**
   ```bash
   export Environment="CUJU-AI-Production"  # or desired environment
   chmod +x ci/scripts/get_environment_variables.sh
   ./ci/scripts/get_environment_variables.sh
   source ./set_env.sh
   ```

2. **Generate Cluster Configurations**
   ```bash
   pip install jinja2 pyyaml
   python ci/scripts/render_cluster_config_template.py
   ```

3. **Deploy Ray Clusters**
   ```bash
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_1.yaml
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_2.yaml
   ./ci/scripts/deploy_ray_cluster.sh $Environment ray_cluster_3.yaml
   ```

4. **Deploy Applications**
   ```bash
   pip install awscli ray[default] boto3
   SERVE_CONFIG_FILE="../serve_configs/ray_config_monolith.yaml"
   ray exec ray_cluster_1.yaml "serve deploy $SERVE_CONFIG_FILE"
   ray exec ray_cluster_1.yaml "python /home/<USER>/ray_jobs/check_serve_status.py"
   ```

5. **Verify Deployment**
   ```bash
   # Connect to cluster for monitoring
   ./ci/scripts/connect_to_cluster.sh
   # Access Ray Dashboard at http://localhost:8265
   # Access Grafana at http://localhost:3000
   ```

### Emergency Procedures

#### Quick Recovery Steps
1. **Identify Failed Component**: Check pipeline logs and monitoring dashboards
2. **Isolate Issue**: Determine if failure is in infrastructure, application, or configuration
3. **Execute Targeted Fix**: Use appropriate manual script or direct cluster access
4. **Verify Recovery**: Run health checks and monitoring validation
5. **Document Issue**: Log incident details for future prevention

#### Rollback Procedures
1. **Identify Last Known Good State**: Check deployment database logs
2. **Retrieve Previous Configuration**: Use git history or artifact storage
3. **Execute Rollback**: Deploy previous version using manual scripts
4. **Validate Rollback**: Ensure system stability and functionality

## Troubleshooting Common Issues

### Pipeline Failures
- **Authentication Issues**: Verify AWS credentials and CodeArtifact tokens
- **Resource Constraints**: Check AWS service limits and quotas
- **Configuration Errors**: Validate YAML syntax and template rendering
- **Network Issues**: Verify VPC, subnet, and security group configurations

### Manual Deployment Issues
- **SSH Access**: Ensure proper key permissions and network connectivity
- **Ray Cluster Issues**: Check cluster logs and resource allocation
- **Application Deployment**: Verify serve configuration and dependencies
- **Monitoring Access**: Confirm port forwarding and tunnel setup

## Best Practices

### Pipeline Management
- **Environment Isolation**: Keep configurations separate per environment
- **Gradual Rollouts**: Test in integration before production deployment
- **Monitoring Integration**: Always verify deployment health before completion
- **Artifact Versioning**: Use meaningful tags and maintain version history

### Manual Deployment
- **Documentation**: Always document manual interventions
- **Verification**: Run comprehensive health checks after manual changes
- **Communication**: Notify team of manual deployments and their reasons
- **Cleanup**: Ensure proper resource cleanup after manual operations

## Conclusion

The CI/CD pipeline provides robust automation with comprehensive fallback options for manual intervention. The combination of Bitbucket Pipelines, AWS infrastructure, and Ray clusters ensures reliable deployment capabilities with multiple monitoring and recovery mechanisms.

For additional support or questions, refer to the individual script documentation in the `ci/scripts/` directory or consult the team's deployment runbooks.
