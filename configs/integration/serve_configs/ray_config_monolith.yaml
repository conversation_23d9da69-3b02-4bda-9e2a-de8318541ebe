# https://docs.ray.io/en/latest/serve/production-guide/config.html#serve-in-production-config-file
# proxy_location: Disabled  # You can disable the HTTP processing

logging_config:
  encoding: JSON
  log_level: INFO

applications:
  - name: E2EMonolith
    import_path: cuju_e2e_service.app_builders.monolith:pipe_builder
    deployments:
      - name: RaySQSIngress
        num_replicas: 5
        ray_actor_options:
          num_cpus: 0.5
          resources:
            node_type:cpu_only: 0.001
      - name: PipeRunnerDeployment
        max_ongoing_requests: 1
        max_replicas_per_node: 1
        graceful_shutdown_wait_loop_s: 30
        graceful_shutdown_timeout_s: 1800
        autoscaling_config:
          min_replicas: 1
          initial_replicas: 5
          max_replicas: 5
          target_ongoing_requests: 1
          upscale_delay_s: 10
          downscale_delay_s: 1800
        ray_actor_options:
          num_cpus: 2
          num_gpus: 1
          resources:
            node_type:gpu: 0.001
      - name: ProcessingEgress
        max_ongoing_requests: 1
        autoscaling_config:
          min_replicas: 1
          max_replicas: 5
          target_ongoing_requests: 1
        ray_actor_options:
          num_cpus: 0.1
          resources:
            node_type:cpu_only: 0.001
      - name: TimeEstimationEgress
        max_ongoing_requests: 1
        autoscaling_config:
          min_replicas: 1
          max_replicas: 5
          target_ongoing_requests: 1
        ray_actor_options:
          num_cpus: 0.1
          resources:
            node_type:cpu_only: 0.001
      - name: CujuMonolithRunner
        max_ongoing_requests: 1
        graceful_shutdown_wait_loop_s: 30
        graceful_shutdown_timeout_s: 2100
        autoscaling_config:
          min_replicas: 2
          initial_replicas: 5
          max_replicas: 5
          target_ongoing_requests: 1
          upscale_delay_s: 10
          downscale_delay_s: 1800
        ray_actor_options:
          num_cpus: 0.5
          resources:
            node_type:cpu_only: 0.001
      - name: TimeEstimation
        max_ongoing_requests: 1
        autoscaling_config:
          min_replicas: 1
          max_replicas: 5
          target_ongoing_requests: 1
        ray_actor_options:
          num_cpus: 1
          resources:
            node_type:cpu_only: 0.001
    args:
      flow_watcher_args:
        enable_flow_watcher: true
        database_mode: "remote"
        root_deployment_name: "RaySQSIngress"
        flush_interval: 10
      ray_sqs_ingress_args:
        source_queue_url: "https://sqs.eu-central-1.amazonaws.com/137068251345/testbench-exercise-to-analyze"
        max_number_of_messages: 10
      egress_args:
        sqs_target_url_processing: "https://sqs.eu-central-1.amazonaws.com/137068251345/testbench-exercise-analysis-result"
        sqs_target_url_estimated_time: "https://sqs.eu-central-1.amazonaws.com/137068251345/testbench-exercise-processing-time"
      pipeline_args:
        pipe_runner_params:
          pipe_config_file: "e2e-pipelines/scene/pipe_e2e_monolith-scene.json.j2"
          settings_name: "settings_loop"
          gc_frequency: 200
          settings_toml: "params_e2e.toml"
          pipe_config_args:
            use_local_input: false
            use_local_model: false
            use_local_output: false
            # settings_toml overrides using jinja variables
            data_bucket_name: ai-bucket-137068251345
            model_store_bucket_name: ai-bucket-137068251345
            vidproc_probe_store_video: true
            ball_probe_store_video: false
            person_probe_store_video: false
