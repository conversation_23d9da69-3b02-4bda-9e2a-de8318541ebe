head_node:
  ami_id: ami-0e9351b108d2118f6
  docker_image: 651706744076.dkr.ecr.eu-central-1.amazonaws.com/cuju-ai-workbench
  docker_image_tag: stablev4
cpu_worker_nodes:
  min_workers: 1
  max_workers: 10
  instance_type: t2.xlarge
  resources: {"CPU": 4, "node_type:cpu_only": 1}
  ami_id: ami-0e9351b108d2118f6
  docker_image: 651706744076.dkr.ecr.eu-central-1.amazonaws.com/cuju-ai-workbench
  docker_image_tag: stablev4
gpu_worker_node_groups:
  - name: g4dn
    instance_type: g4dn.xlarge
    ami_id: ami-0ed7a543fa0afb8ae
    docker_image: 651706744076.dkr.ecr.eu-central-1.amazonaws.com/cuju-ai-workbench-cuda
    docker_image_tag: stablev4
    min_workers: 1
    max_workers: 10
    resources:
      CPU: 4
      GPU: 1
      node_type:gpu: 1
