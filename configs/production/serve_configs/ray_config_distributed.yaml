# https://docs.ray.io/en/latest/serve/production-guide/config.html#serve-in-production-config-file
# proxy_location: Disabled  # You can disable the HTTP processing

applications:
  - name: E2EDistributed
    import_path: cuju_e2e_service.app_builders.distributed:pipe_builder
    deployments:
    - name: RaySQSIngress
      # max_ongoing_requests: 20
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        # target_ongoing_requests: 10
      ray_actor_options:
        num_cpus: 0.1
    - name: vst
      # max_ongoing_requests: 20
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        # target_ongoing_requests: 10
      ray_actor_options:
        num_cpus: 0.1
    - name: ball
      max_ongoing_requests: 2
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        target_ongoing_requests: 2
      ray_actor_options:
        num_cpus: 0.5
    - name: person
      max_ongoing_requests: 2
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        target_ongoing_requests: 2
      ray_actor_options:
        num_cpus: 0.5
        num_gpus: 1
    - name: cones
      # max_ongoing_requests: 20
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        # target_ongoing_requests: 10
      ray_actor_options:
        num_cpus: 0.1
    - name: CujuExerciseRunner
      # max_ongoing_requests: 20
      autoscaling_config:
        min_replicas: 1
        max_replicas: 1
        # target_ongoing_requests: 10
      ray_actor_options:
        num_cpus: 0.1
    args:
      enable_flow_watcher: true
      ray_sqs_ingress_args:
        max_retries: 2
        source_queue_url: "https://sqs.eu-central-1.amazonaws.com/235103184542/ray-sandbox-in-queue-1"
        target_queue_url: "https://sqs.eu-central-1.amazonaws.com/235103184542/ray-sandbox-out-queue-1"
        max_number_of_messages: 10
      pipelines:
        vst:
          pipe_runner_params:
            pipe_config_file:  "e2e-pipelines/vst/pipe_e2e_vst-s3.json"
            plugin_toml: "pyproject.toml"
            settings_name: "settings_loop"
            gc_frequency: 1
            settings_toml: "params_e2e.toml"
        ball:
          pipe_runner_params:
            pipe_config_file:  "e2e-pipelines/ball/pipe_e2e_ball-s3.json"
            plugin_toml: "pyproject.toml"
            settings_name: "settings_loop"
            gc_frequency: 1
            settings_toml: "params_e2e.toml"
        person:
          pipe_runner_params:
            pipe_config_file:  "e2e-pipelines/person/pipe_e2e_person-s3.json"
            plugin_toml: "pyproject.toml"
            settings_name: "settings_loop"
            gc_frequency: 1
            settings_toml: "params_e2e.toml"
        cones:
          pipe_runner_params:
            pipe_config_file:  "e2e-pipelines/cones/pipe_e2e_cones-s3.json"
            plugin_toml: "pyproject.toml"
            settings_name: "settings_loop"
            gc_frequency: 1
            settings_toml: "params_e2e.toml"