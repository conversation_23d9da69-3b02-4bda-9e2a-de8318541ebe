packer {
  required_plugins {
    amazon = {
      version = ">= 1.2.8"
      source  = "github.com/hashicorp/amazon"
    }
  }
}


variable "aws_region" {
  type    = string
  default = "eu-central-1"
}

variable "aws_access_key" {
  type      = string
  sensitive = true
}

variable "aws_secret_key" {
  type      = string
  sensitive = true
}

variable "source_ami" {
  type    = string
  default = "ami-0abcdef1234567890"
}

variable "instance_type" {
  type    = string
  default = "t3.xlarge"
}

variable "ecr_repository_name" {
  type = string
}

variable "image_tag" {
  type = string
}

variable "vpc_id" {
    type = string
    default = "vpc-01fcd0ead09436d66"
}

variable "subnet_id" {
    type = string
    default = "subnet-0ef71eb8b029d3d2a"
}

variable "customization_script" {
  type    = string
  default = "./user_data.sh"
}

variable "ami_name" {
  type    = string
  default = "custom-ray-ami"
}

variable "enable_nvidia_runtime" {
  default = false
}

variable "ami_share_account_ids" {
  type    = list(string)
  default = []
}

source "amazon-ebs" "custom_ami" {
  region         = var.aws_region
  source_ami     = var.source_ami
  instance_type  = var.instance_type
  associate_public_ip_address = true
  ssh_username   = "ubuntu"
  ami_name       = var.ami_name
  vpc_id         = var.vpc_id
  subnet_id      = var.subnet_id
  ami_users      = var.ami_share_account_ids

  launch_block_device_mappings {
    device_name = "/dev/sda1"
    volume_size = 140
    volume_type = "gp3"
  }

  aws_polling {
    delay_seconds = 200
    max_attempts  = 50
  }

}

build {
  name    = "packer"
  sources = [
    "source.amazon-ebs.custom_ami"
  ]
  provisioner "file" {
      source      = var.customization_script
      destination = "/tmp/user_data.sh"
  }

  provisioner "shell" {
      inline = [
        "export AWS_ACCESS_KEY_ID='${var.aws_access_key}'",
        "export AWS_SECRET_ACCESS_KEY='${var.aws_secret_key}'",
        "export AWS_DEFAULT_REGION='${var.aws_region}'",
        "chmod +x /tmp/user_data.sh",
        "sudo -E /tmp/user_data.sh '${var.aws_region}' '${var.ecr_repository_name}' '${var.image_tag}' '${var.enable_nvidia_runtime}'"
      ]
  }
}
