#!/bin/bash

set -e

AWS_REGION=$1
ECR_REPOSITORY_NAME=$2
IMAGE_TAG=$3
ENABLE_NVIDIA_RUNTIME="$4"

curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Create /etc/docker/daemon.json with AWS CloudWatch Logs configuration
sudo bash -c 'cat <<EOL > /etc/docker/daemon.json
{
  "log-driver": "awslogs",
  "log-opts": {
    "awslogs-region": "eu-central-1",
    "awslogs-group": "ray_cluster"
  }
}
EOL'

# The nvidia-ctk command modifies the /etc/docker/daemon.json file on the host. The file is updated so that Dock<PERSON> can use the NVIDIA Container Runtime.
if [ "$ENABLE_NVIDIA_RUNTIME" = "true" ]; then
    echo "Configuring NVIDIA runtime for Docker..."
    sudo nvidia-ctk runtime configure --runtime=docker
else
    echo "Skipping NVIDIA runtime configuration"
fi

# Restart Docker to apply the new configuration
sudo systemctl restart docker

# Start the Docker service
sudo systemctl start docker

# Enable Docker to start on boot
sudo systemctl enable docker

# Add the ubuntu to the docker group
sudo usermod -aG docker ubuntu

sudo snap install aws-cli --classic

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.29.7/docker-compose-linux-x86_64" -o /usr/local/bin/docker-compose

# Ensure the file has executable permissions
sudo chmod +x /usr/local/bin/docker-compose

# Create Docker Compose directory
mkdir /home/<USER>/docker-setup
cd /home/<USER>/docker-setup

sudo mkdir -p /fluentbit/logs
sudo chmod 777 /fluentbit/logs

cat <<EOL > docker-compose.yml
services:
  fluentbit:
    image: fluent/fluent-bit:latest
    volumes:
      - /var/log/ray_logs:/var/log/ray_logs:ro
      - ./fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
      - ./parsers.conf:/fluent-bit/etc/parsers.conf:ro # If you have parsers.conf
      - ./validate_log.lua:/fluent-bit/etc/validate_log.lua:ro
      - /fluentbit/logs:/fluentbit/logs
    command: ["/fluent-bit/bin/fluent-bit", "--config", "/fluent-bit/etc/fluent-bit.conf"]
    environment:
      CLUSTER_NAME: "${CLUSTER_NAME:-default_cluster}"
      NAME: "${NAME}"
    logging:
      driver: awslogs
      options:
        awslogs-region: eu-central-1
        awslogs-group: "ray_cluster"
        awslogs-stream: "${CLUSTER_NAME}_${NAME}_fluent-bit_container"
EOL

cat << 'EOF' > validate_log.lua
function cb_validate(tag, ts, record)
    -- Ensure the record is a table
    if type(record) ~= "table" then
        return -1, 0, 0
    end

    -- Keep only if both "time" and "level" keys exist
    if record["time"] ~= nil and record["level"] ~= nil then
        return 1, ts, record
    end

    -- Drop if either is missing
    return -1, 0, 0
end
EOF

# Create fluent-bit.conf
cat <<EOL > fluent-bit.conf
[SERVICE]
    Flush        1
    Daemon       Off
    Log_Level    info
    Parsers_File parsers.conf

[INPUT]
    Name              tail
    Path              /var/log/ray_logs/session_*/logs/worker*.err
    Parser            loguru_json
    Tag               worker_err

[INPUT]
    Name              tail
    Path              /var/log/ray_logs/session_*/logs/worker*.out
    Parser            loguru_json
    Tag               worker_out


[INPUT]
    Name              tail
    Path              /var/log/ray_logs/session_*/logs/serve/controller_*.log
    Exclude_Path      /var/log/ray_logs/session_*/logs/serve/controller_build_*.log
    Parser            loguru_json
    Tag               controller

[FILTER]
    Name   lua
    Match  worker_*
    script validate_log.lua
    call   cb_validate

[OUTPUT]
    Name        file
    Match       controller
    Path        /fluentbit/logs/
    File        debug.log

[OUTPUT]
    Name              cloudwatch_logs
    Match             worker_*
    region            eu-central-1
    log_group_name    ray_cluster
    log_stream_prefix ${CLUSTER_NAME}_${NAME}_
    auto_create_group true
EOL

# Create parsers.conf (optional)
cat <<EOL > parsers.conf
[PARSER]
    Name        loguru_json
    Format      json
    Time_Key    time
    Time_Format %s.%L
    Time_Keep   On
EOL

# Change ownership to ubuntu
chown -R ubuntu:ubuntu /home/<USER>/docker-setup

sudo mkdir -p /var/log/ray_logs
sudo chmod 777 /var/log/ray_logs

# Run Docker Compose as ubuntu
sudo -u ubuntu /usr/local/bin/docker-compose pull

# Run docker pull on custom ray container
aws ecr get-login-password --region $AWS_REGION | sudo docker login --username AWS --password-stdin 651706744076.dkr.ecr.$AWS_REGION.amazonaws.com
sudo docker pull 651706744076.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPOSITORY_NAME:$IMAGE_TAG


