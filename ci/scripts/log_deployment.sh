#!/bin/bash
set -euo pipefail

if [ "$#" -ne 3 ]; then
  echo "Usage: $0 <ray_cluster_name> <ray_cluster_yaml> <ray_serve_config_yaml>"
  exit 1
fi

RAY_CLUSTER_NAME=$1
RAY_CLUSTER_CONFIG=$2
RAY_SERVE_CONFIG=$3

TAG=${BITBUCKET_TAG:-unknown}
PULL_REQUEST_ID=${BITBUCKET_PR_ID:-unknown}

echo "Ray cluster name: $RAY_CLUSTER_NAME"
echo "Ray cluster config: $RAY_CLUSTER_CONFIG"
echo "Ray Serve config: $RAY_SERVE_CONFIG"

ray exec "$RAY_CLUSTER_CONFIG" "ray job submit \
    --working-dir /home/<USER>/ray_jobs \
    -- python log_deployment_to_db.py \
    --cluster_name \"$RAY_CLUSTER_NAME\" \
    --ai_inference_commit \"$BITBUCKET_COMMIT\" \
    --bitbucket_pipeline_url \"$BITBUCKET_GIT_HTTP_ORIGIN/pipelines/results/$BITBUCKET_BUILD_NUMBER\" \
    --branch \"$BITBUCKET_BRANCH\" \
    --tag \"$TAG\" \
    --pull_request_id \"$PULL_REQUEST_ID\" \
    --deployed_serve_config_file \"$RAY_SERVE_CONFIG\" \
    "
