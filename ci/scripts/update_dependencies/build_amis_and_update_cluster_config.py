import os
import subprocess
import re
import argparse
import json
import yaml

ENVIRONMENTS = {
    "CUJU-AI-Integration": "integration",
    "CUJU-AI-Test": "test",
    "CUJU-AI-Production": "production",
}

def build_ami(packer_vars, template_path):
    subprocess.run(["packer", "init", template_path], check=True)
    result = subprocess.run(
        ["packer", "build"] + [f"-var={k}={v}" for k, v in packer_vars.items()] + [template_path],
        capture_output=True,
        text=True
    )
    print(result.stdout)
    match = re.search(r'AMI: (ami-[a-z0-9]+)', result.stdout)
    if not match:
        raise RuntimeError("AMI ID not found in output")
    return match.group(1)

def update_cluster_config(config_path, cpu_ami_id, gpu_ami_id, docker_image_tag):
    with open(config_path) as f:
        config = yaml.safe_load(f)

    if cpu_ami_id:
        config["head_node"]["ami_id"] = cpu_ami_id
        config["cpu_worker_nodes"]["ami_id"] = cpu_ami_id
        config["head_node"]["docker_image_tag"] = docker_image_tag
        config["cpu_worker_nodes"]["docker_image_tag"] = docker_image_tag

    if gpu_ami_id:
        if "gpu_worker_node_groups" in config:
            for group in config["gpu_worker_node_groups"]:
                group["ami_id"] = gpu_ami_id
                group["docker_image_tag"] = docker_image_tag

    with open(config_path, "w") as f:
        yaml.dump(config, f)

    return config_path

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--environment", required=False)
    parser.add_argument("--version", required=True)
    parser.add_argument("--aws-account-id", required=True)
    parser.add_argument("--target", choices=["cpu", "gpu", "both"], default="both")  # NEW
    args = parser.parse_args()

    image_tag = args.version.replace("+", "-")
    common_vars = {
        "aws_region": "eu-central-1",
        "aws_access_key": os.environ["AWS_ACCESS_KEY_ID"],
        "aws_secret_key": os.environ["AWS_SECRET_ACCESS_KEY"],
        "image_tag": image_tag,
        "customization_script": "ci/ami/user_data.sh",
        "ami_share_account_ids": json.dumps([
            "************", "************", "************"
        ])
    }

    cpu_ami = None
    gpu_ami = None

    if args.target in ("cpu", "both"):
        cpu_ami = build_ami({
            **common_vars,
            "source_ami": "ami-03b3b5f65db7e5c6f",
            "ami_name": f"ray-cpu-fluentbit-{image_tag}",
            "ecr_repository_name": "cuju-ai-workbench",
            "enable_nvidia_runtime": "false"
        }, "ci/ami/packer.pkr.hcl")

    if args.target in ("gpu", "both"):
        gpu_ami = build_ami({
            **common_vars,
            "source_ami": "ami-013902832cfbfc436",
            "ami_name": f"ray-gpu-fluentbit-{image_tag}",
            "ecr_repository_name": "cuju-ai-workbench-cuda",
            "enable_nvidia_runtime": "true"
        }, "ci/ami/packer.pkr.hcl")

    if args.environment:
        cluster_config_path = f"configs/{ENVIRONMENTS[args.environment]}/cluster_config.yaml"
        path = update_cluster_config(cluster_config_path, cpu_ami, gpu_ami, image_tag)
        print(f"CLUSTER_CONFIG_PATH={path}")
