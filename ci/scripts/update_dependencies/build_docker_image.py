import subprocess
import os
import argparse

def build_and_push_docker_image(
        image_name,
        dockerfile_path,
        context_dir,
        image_tag,
        aws_account_id,
        environment=None,
        install_dependencies=False
):
    ecr_repo = f"{aws_account_id}.dkr.ecr.eu-central-1.amazonaws.com/{image_name}:{image_tag}"
    build_cmd = [
        "docker", "build", "-t", ecr_repo, "-f", dockerfile_path, context_dir,
        "--build-arg", f"ENVIRONMENT={'production' if not environment else environment}",
        "--build-arg", f"INSTALL_CODEARTIFACT_DEPS={'true' if install_dependencies else 'false'}"
    ]
    if install_dependencies:
        build_cmd.extend([
            "--secret", f"id=aws_codeartifact_token,env=CODEARTIFACT_AUTH_TOKEN"
        ])
    subprocess.run(build_cmd, check=True)
    subprocess.run(["docker", "push", ecr_repo], check=True)
    return ecr_repo

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--image-name", required=True)
    parser.add_argument("--dockerfile-path", required=True)
    parser.add_argument("--context-dir", default=".")
    parser.add_argument("--image-tag", required=True)
    parser.add_argument("--aws-account-id", required=True)
    parser.add_argument("--environment", required=False)
    parser.add_argument("--install-deps", action="store_true")
    args = parser.parse_args()

    build_and_push_docker_image(
        image_name=args.image_name,
        dockerfile_path=args.dockerfile_path,
        context_dir=args.context_dir,
        image_tag=args.image_tag,
        aws_account_id=args.aws_account_id,
        environment=args.environment,
        install_dependencies=args.install_deps
    )

