from pathlib import Path
from jinja2 import Environment, FileSystemLoader
import os
import argparse

ENVIRONMENTS = {
    "CUJU-AI-Integration": "integration",
    "CUJU-AI-Test": "test",
    "CUJU-AI-Production": "production",
}

def render_pyproject(package_versions: dict, environment: str):
    script_dir = Path(__file__).resolve().parent
    template_dir = script_dir.parent.parent / "templates"
    env = Environment(loader=FileSystemLoader(str(template_dir)))
    template = env.get_template("pyproject.toml.j2")

    rendered = template.render(package_versions=package_versions)
    target_dir = f"configs/{environment}/app_configs"
    os.makedirs(target_dir, exist_ok=True)
    output_path = f"{target_dir}/pyproject.toml"
    with open(output_path, "w") as f:
        f.write(rendered)
    return output_path

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--environment", required=True)
    parser.add_argument("--version", required=True)
    args = parser.parse_args()

    env_dir = ENVIRONMENTS[args.environment]
    version = args.version
    package_versions = {"cuju-e2e-service": version}

    path = render_pyproject(package_versions, env_dir)
    print(f"PYPROJECT_PATH={path}")
