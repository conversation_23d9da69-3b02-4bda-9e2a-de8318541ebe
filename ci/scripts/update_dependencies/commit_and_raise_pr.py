import subprocess
import os
import requests
import argparse
from datetime import datetime

ENVIRONMENTS = {
    "CUJU-AI-Integration": "integration",
    "CUJU-AI-Test": "test",
    "CUJU-AI-Production": "production",
}

REPO_ACCESS_TOKEN = os.environ.get("ADMIN_CREATE_PR_TOKEN")

def commit_changes(branch_name, message, push_to_main):
    subprocess.run(["git", "config", "--global", "user.name", "Release Bot"], check=True)
    subprocess.run(["git", "config", "--global", "user.email", "<EMAIL>"], check=True)
    subprocess.run(["git", "checkout", "-b", branch_name], check=True)
    subprocess.run(["git", "add", "."], check=True)
    subprocess.run(["git", "commit", "-m", message], check=True)

    if push_to_main:
        subprocess.run(["git", "checkout", "main"], check=True)
        subprocess.run(["git", "merge", branch_name], check=True)
        subprocess.run(["git", "push", "origin", "main"], check=True)
    else:
        subprocess.run(["git", "push", "-u", "origin", branch_name], check=True)

def create_pr(branch_name, environment):
    url = "https://api.bitbucket.org/2.0/repositories/rogontechnologies/cuju-ai-inference-server/pullrequests"
    headers = {"Authorization": f"Bearer {REPO_ACCESS_TOKEN}"}
    payload = {
        "title": f"Update pyproject.toml for {environment}",
        "source": {"branch": {"name": branch_name}},
        "destination": {"branch": {"name": "main"}},
        "description": f"Auto-generated update for {environment} dependencies"
    }

    r = requests.post(url, headers=headers, json=payload)
    if r.status_code != 201:
        raise Exception(f"Failed to create PR: {r.text}")
    print(f"PR created: {r.json()['links']['html']['href']}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--environment", required=True)
    parser.add_argument("--version", required=True)
    args = parser.parse_args()

    env_key = ENVIRONMENTS[args.environment]
    version_tag = args.version.replace(".", "-")
    timestamp = datetime.utcnow().strftime("%Y%m%d%H%M%S")
    branch = f"update-deps-{env_key}-{version_tag}-{timestamp}"
    msg = f"Update dependencies for {env_key}"

    if env_key == "integration":
        commit_changes(branch, msg, push_to_main=True)
    else:
        commit_changes(branch, msg, push_to_main=False)
        create_pr(branch, env_key)
