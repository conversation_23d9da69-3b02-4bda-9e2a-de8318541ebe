import argparse
from pathlib import Path

import boto3
import jinja2
import tomli

# AWS parameters
DOMAIN = "cuju-ai"
REPOSITORY = "cuju-ai-workbench"
REGION = "eu-central-1"
PROFILE = "shared"


# Initialize the boto3 session using the shared profile
session = boto3.Session(profile_name=PROFILE, region_name=REGION)
codeartifact_client = session.client("codeartifact")


def get_all_packages(domain: str, repository: str) -> dict:
    """Fetch all packages in the specified CodeArtifact repository."""
    response = codeartifact_client.list_packages(
        domain=domain, repository=repository, format="pypi"
    )
    return response["packages"]


def get_package_versions(domain: str, repository: str, package: str) -> list[dict]:
    """Fetch all versions of a given package."""
    response = codeartifact_client.list_package_versions(
        domain=domain, repository=repository, package=package, format="pypi"
    )
    return response["versions"]


def get_version_details(
    domain: str, repository: str, package: str, version: str
) -> dict:
    """Fetch details about a specific version of a package."""
    response = codeartifact_client.describe_package_version(
        domain=domain,
        repository=repository,
        package=package,
        packageVersion=version,
        format="pypi",
    )
    return response["packageVersion"]


def get_latest_version(domain, repository, package):
    """Get the latest published version for a package based on published time."""
    versions = get_package_versions(domain, repository, package)

    latest_version = None
    latest_published_time = None

    for version in versions:
        version_details = get_version_details(
            domain, repository, package, version["version"]
        )
        published_time = version_details.get("publishedTime", None)

        if published_time and (
            not latest_published_time or published_time > latest_published_time
        ):
            latest_published_time = published_time
            latest_version = version["version"]

    return latest_version


def main(dependencies_pyproject_toml_path: str, output_dir: str):
    # Load existing pyproject.toml file
    with Path(dependencies_pyproject_toml_path).open("rb") as f:
        input_pyproject = tomli.load(f)

    required_dependencies = input_pyproject["project"]["dependencies"]
    required_dependencies.append("cuju-e2e-service")

    # List all packages
    packages = get_all_packages(DOMAIN, REPOSITORY)

    if not packages:
        print("No packages found.")
        return

    print("Package Name | Latest Version")
    print("-----------------------------")

    render_data = {"package_versions": {}}

    for package in packages:
        package_name: str = package["package"]

        if package_name not in required_dependencies or (
            not package_name.startswith("cuju") and package_name != "imgeta"
        ):
            continue

        # Get the latest version of the package
        latest_version = get_latest_version(DOMAIN, REPOSITORY, package_name)

        if latest_version:
            render_data["package_versions"][package_name] = latest_version
            print(f"{package_name} | {latest_version}")
        else:
            print(f"{package_name} has no valid published versions.")

    script_dir = Path(__file__).resolve().parent
    template_dir = script_dir.parent / "templates"

    template_loader = jinja2.FileSystemLoader(searchpath=str(template_dir))
    template_env = jinja2.Environment(loader=template_loader, autoescape=True)
    template = template_env.get_template("pyproject.toml.j2")

    output_file_path = Path(output_dir) / "pyproject.toml"
    with output_file_path.open("w") as f:
        f.write(template.render(render_data))

    print("Generated pyproject.toml location:", output_file_path)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-i",
        "--dependencies_pyproject_toml",
        help="The path to the pyproject.toml containing the required dependencies",
        required=True,
        type=str,
    )
    parser.add_argument(
        "-o",
        "--output_dir",
        help="The directory to generate the pyproject.toml with pinned versions to",
        type=str,
        default="./",
    )
    args = parser.parse_args()
    main(args.dependencies_pyproject_toml, args.output_dir)
