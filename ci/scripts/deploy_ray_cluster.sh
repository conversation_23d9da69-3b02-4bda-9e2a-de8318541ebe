#!/bin/bash
set -euo pipefail

if [ "$#" -ne 2 ]; then
  echo "Usage: $0 <environment> <ray_cluster_yaml>"
  exit 1
fi

ENVIRONMENT=$1
RAY_CLUSTER_CONFIG=$2

echo "Deploying Ray cluster using config: $RAY_CLUSTER_CONFIG in environment: $ENVIRONMENT"

source ./set_env.sh

# Install dependencies
pip install jinja2 awscli ray[default] boto3
apt-get update && apt-get install -y rsync

# Set up AWS credentials and config
mkdir -p ~/.aws
echo "[default]" > ~/.aws/credentials
echo "[default]" > ~/.aws/config
echo "region = eu-central-1" >> ~/.aws/config
echo "output = json" >> ~/.aws/config
cp ~/.aws/config ./aws_config
echo "aws_access_key_id = ${AWS_ACCESS_KEY_ID}" >> ~/.aws/credentials
echo "aws_secret_access_key = ${AWS_SECRET_ACCESS_KEY}" >> ~/.aws/credentials
cp ~/.aws/credentials ./aws_credentials

# Deploy the Ray cluster
ray up "$RAY_CLUSTER_CONFIG" --yes
