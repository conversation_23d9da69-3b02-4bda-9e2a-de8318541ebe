#!/bin/bash

set -euo pipefail

ENVIRONMENT=$1
CLUSTER_YAML=$2
CLUSTER_TAG=$3

source ./set_env.sh
pip install awscli ray[default] boto3

echo "Getting instance ID(s) for head node of $CLUSTER_TAG..."
aws ec2 describe-instances \
  --filters "Name=tag:Name,Values=${CLUSTER_TAG}" "Name=instance-state-name,Values=running" \
  --query "Reservations[].Instances[].InstanceId" \
  --output text > head_node_instance_ids.txt

echo "Tearing down cluster defined in $CLUSTER_YAML..."
ray down "$CLUSTER_YAML" --yes &

while read -r instance_id; do
  while true; do
    state=$(aws ec2 describe-instances --instance-ids "$instance_id" \
      --query "Reservations[].Instances[].State.Name" --output text)
    if [[ "$state" == "stopped" || "$state" == "terminated" ]]; then
      echo "Instance $instance_id is $state"
      break
    else
      echo "Waiting for instance $instance_id to stop/terminate. Current state: $state"
      sleep 5
    fi
  done
done < head_node_instance_ids.txt
