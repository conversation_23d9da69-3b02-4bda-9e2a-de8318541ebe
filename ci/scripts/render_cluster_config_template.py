import yaml
import jinja2
import sys
import argparse
from pathlib import Path


parser = argparse.ArgumentParser(description="Render Ray Cluster Configuration")

parser.add_argument("--config", required=True, help="Path to the static config.yaml file")
parser.add_argument("--cluster_name", type=str, required=True, help="Cluster name")
parser.add_argument("--region", type=str, required=True, help="AWS region")
parser.add_argument("--availability_zone", type=str, required=True, help="AWS availability zone")
parser.add_argument("--subnet_id", type=str, required=True, help="Subnet ID")
parser.add_argument("--security_group_ids", type=str, required=True, help="Security group ID")
parser.add_argument("--iam_instance_profile", type=str, required=True, help="AWS region")
parser.add_argument("--app_path", type=str, required=True, help="App path")
parser.add_argument("--serve_configs_path", type=str, required=True, help="Serve configs path")
parser.add_argument("--aws_config", type=str, required=True, help="Path to AWS config file")
parser.add_argument("--jumphost_ip", type=str, required=True, help="Jumphost IP address")
parser.add_argument("--ssh_key_name", type=str, required=True, help="AWS EC2 SSH key name")
parser.add_argument("--account_id", type=str, required=True, help="AWS Account ID")
parser.add_argument("--shared_account_id", type=str, required=True, help="AWS Shared Account ID")
parser.add_argument("--monitoring_instance_ip", type=str, required=True, help="Private IP address for monitoring instance")
parser.add_argument("--detailed_monitoring", type=str, required=True, help="Allow detailed monitoring")
parser.add_argument("--install_dependencies", type=str, required=True, help="Require to install workbench dependencies")

args = parser.parse_args()

def load_yaml(file_path):
    """Load a YAML config file."""
    with open(file_path, "r") as file:
        return yaml.safe_load(file)

config_data = load_yaml(args.config)

head_node = config_data.get("head_node", {})
cpu_worker_nodes = config_data.get("cpu_worker_nodes", {})
gpu_worker_node_groups = config_data.get("gpu_worker_node_groups", {})

cluster_vars = {
    'cluster_name': args.cluster_name,
    'region': args.region,
    'availability_zone': args.availability_zone,
    'subnet_id': args.subnet_id,
    'security_group_ids': args.security_group_ids,
    'iam_instance_profile': args.iam_instance_profile,
    'app_path': args.app_path,
    'serve_configs_path': args.serve_configs_path,
    'aws_config': args.aws_config,
    'jumphost_ip': args.jumphost_ip,
    'ssh_key_name': args.ssh_key_name,
    'account_id': args.account_id,
    'shared_account_id': args.shared_account_id,
    'monitoring_instance_ip': args.monitoring_instance_ip,
    'head_node': head_node,
    'cpu_worker_nodes': cpu_worker_nodes,
    'gpu_worker_node_groups': gpu_worker_node_groups,
    'detailed_monitoring': True if args.detailed_monitoring == "True" else False,
    'install_dependencies': True if args.install_dependencies == "True" else False,
}

script_dir = Path(__file__).resolve().parent
template_dir = script_dir.parent / "templates"

template_loader = jinja2.FileSystemLoader(searchpath=str(template_dir))
template_env = jinja2.Environment(loader=template_loader)
template = template_env.get_template("cluster-config.j2")

sys.stdout.write(template.render(cluster_vars))
