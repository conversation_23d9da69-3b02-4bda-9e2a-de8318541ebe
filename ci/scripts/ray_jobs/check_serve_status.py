import time
import sys
import ray
from ray import serve
from loguru import logger

def wait_until_all_deployments_ready(timeout=6000, interval=30):
    ray.init(address="auto")  # Connect to the existing cluster
    serve.start(detached=True)
    start = time.time()
    while time.time() - start < timeout:
        status = serve.status()
        deployments = status.applications["E2EMonolith"].deployments
        all_running = all(d.status == "HEALTHY" for d in deployments.values())
        if all_running:
            logger.success("All deployments are running.")
            return
        time.sleep(interval)
    logger.error("Timeout: Not all deployments are running.")
    sys.exit(1)

if __name__ == "__main__":
    wait_until_all_deployments_ready()
