import argparse
import datetime
import sys
from pathlib import Path

import pkg_resources
import ray
import tomli
import yaml
from cuju_e2e_service.flow_watcher.storage.documentdb import DocumentDBStorage
from cuju_e2e_service.schemas.flow_watcher import DocumentDBMode
from cuju_e2e_service.settings import PERSISTENT_STORAGE_DB
from loguru import logger
from pymongo import MongoClient
from ray.util import get_node_ip_address


DB_COLLECTION_NAME = "deployments_log"


def _get_installed_packages() -> dict:
    installed_packages = {d.project_name: d.version for d in pkg_resources.working_set}
    return dict(sorted(installed_packages.items()))


def _read_and_parse_serve_configs() -> dict:
    serve_configs = {}
    for serve_config_file_path in Path("/serve_configs").glob("*.yaml"):
        with serve_config_file_path.open() as f:
            serve_configs[serve_config_file_path.name] = yaml.safe_load(f)

    return serve_configs


def _main(args: argparse.Namespace) -> None:
    db = DocumentDBStorage(mode=DocumentDBMode.REMOTE)

    client = MongoClient(db._get_cluster_uri())

    db = client[PERSISTENT_STORAGE_DB]
    collection = db[DB_COLLECTION_NAME]

    installed_packages = _get_installed_packages()
    cuju_e2e_service_version = installed_packages.get("cuju-e2e-service", "unknown")
    ray_version = installed_packages.get("ray", "unknown")

    logger.info(f"cluster_name: {args.cluster_name}")
    logger.info(f"bitbucket deployment pipeline: {args.bitbucket_pipeline_url}")
    logger.info(f"ai-inference commit: {args.ai_inference_commit}")
    logger.info(f"cuju_e2e_service version: {cuju_e2e_service_version}")
    logger.info(f"ray version: {ray_version}")

    collection.insert_one(
        {
            "deployment_time": datetime.datetime.now(datetime.UTC),
            "bitbucket_info": {
                "ai_inference_commit": args.ai_inference_commit,
                "bitbucket_pipeline_url": args.bitbucket_pipeline_url,
                "branch": args.branch,
                "tag": args.tag,
                "pull_request_id": args.pull_request_id,
            },
            "cluster_name": args.cluster_name,
            "deployed_serve_config_file": args.deployed_serve_config_file,
            "node_ip_address": get_node_ip_address(),
            "cuju_e2e_service_version": cuju_e2e_service_version,
            "ray_version": ray_version,
            "installed_packages": installed_packages,
            "serve_configs": _read_and_parse_serve_configs(),
        }
    )


if __name__ == "__main__":
    ray.init()

    parser = argparse.ArgumentParser()
    parser.add_argument("--cluster_name", required=True)
    parser.add_argument("--ai_inference_commit", required=False, default="unknown")
    parser.add_argument("--bitbucket_pipeline_url", required=False, default="unknown")
    parser.add_argument("--branch", required=False, default="unknown")
    parser.add_argument("--tag", required=False, default="unknown")
    parser.add_argument("--pull_request_id", required=False, default="unknown")
    parser.add_argument(
        "--deployed_serve_config_file", required=False, default="unknown"
    )
    args = parser.parse_args()

    logger.remove()
    logger.add(sys.stdout, level="INFO")
    logger.add(sys.stderr, level="ERROR")
    _main(args)
