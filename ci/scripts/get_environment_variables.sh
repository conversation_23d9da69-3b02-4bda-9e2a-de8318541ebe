#!/bin/bash
set -euo pipefail

echo "Environment: ${Environment:-AI-Integration}"

wget -O- https://apt.releases.hashicorp.com/gpg | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | tee /usr/share/keyrings/hashicorp-archive-keyring.gpg > /dev/null
echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com bookworm main" | tee /etc/apt/sources.list.d/hashicorp.list

apt update && apt install -y lsb-release terraform

export TF_VAR_environment="${Environment:-AI-Integration}"
export TF_VAR_env_short=$(echo "$TF_VAR_environment" | rev | cut -d'-' -f1 | rev | tr '[:upper:]' '[:lower:]')

key_id_name="AWS_ACCESS_KEY_ID_$(echo "$TF_VAR_environment" | tr '[:lower:]-' '[:upper:]_')"
secret_key_name="AWS_SECRET_ACCESS_KEY_$(echo "$TF_VAR_environment" | tr '[:lower:]-' '[:upper:]_')"

export AWS_ACCESS_KEY_ID="${!key_id_name}"
export AWS_SECRET_ACCESS_KEY="${!secret_key_name}"

cd ci/deployment
terraform init
terraform apply -auto-approve

cat set_env_terraform.sh ../set.txt > "$BITBUCKET_CLONE_DIR/set_env.sh"
chmod +x "$BITBUCKET_CLONE_DIR/set_env.sh"
