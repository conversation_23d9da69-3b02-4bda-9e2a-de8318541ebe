#!/bin/bash

options=("CUJU-AI-Integration" "CUJU-AI-Test" "CUJU-AI-Production")
echo "Checking aws configure profiles ..."
for profile_name in "${options[@]}"; do
  aws configure list --profile "$profile_name" >/dev/null 2>/dev/null
  if [ $? -ne 0 ]; then
    echo "Not all available aws profiles set up."
    echo "Use 'aws configure' to select the correct AWS Account for the environment: INT, TEST, PROD".
    echo "You can automate this step by setting up all profiles with the exact name of the environment. Example:"
    echo "aws configure --profile CUJU-AI-Integration"
    echo
    break
  fi
done

echo "Please select an environment:"
PS3=$'\n  \e[33mEnter selection:\e[0m '
select opt in "${options[@]}"; do
    case $REPLY in
        1)
            environment="CUJU-AI-Integration"
            break
            ;;
        2)
            environment="CUJU-AI-Test"
            break
            ;;
        3)
            environment="CUJU-AI-Production"
            break
            ;;
        *)
            echo "Invalid option. Please try again."
            ;;
    esac
done
unset PS3

# Check if the selected profile exists and set it
aws configure list --profile "$environment" >/dev/null 2>/dev/null
if [ $? -ne 0 ]; then
  echo "Warning: profile $environment not found in aws configure list. Make sure you have set it as default."
else
  echo "Using stored profile $environment."
  export AWS_PROFILE=${environment}
fi

JUMP_HOST="${environment}-jumphost-ray-clusters"
TARGET_HOST="ray-cluster_1-head"
MONITORING_HOST="${environment}-monitoring"
SSM_KEY_PARAMETER_NAME="/ray-clusters/jumphost_ssh_key"
LOCAL_KEY_PATH="$HOME/.ssh/ray_jump_host.pem"

# Ensure jq is installed
if ! command -v jq &> /dev/null
then
    echo "jq could not be found, please install jq to run this script."
    exit 1
fi

# Function to get EC2 instance ID and public IP (or public DNS) by instance name
#
# Usage:
#   get_instance_info "instance-name"
#
# Output on success (to stdout, space-separated):
#   <instance_id> <public_ip_or_dns>
get_instance_info() {
  local instance_name="$1"
  local instance_info
  local instance_id
  local public_ip
  local public_dns_name
  local connection_target

  if [[ -z "$instance_name" ]]; then
    echo "Error: Instance name argument is required." >&2
    return 1
  fi

  echo "Fetching instance information for: ${instance_name}" >&2 # Output progress to stderr

  # Get Instance ID, Public IP Address, and Public DNS Name
  instance_info=$(aws ec2 describe-instances \
    --filters "Name=tag:Name,Values=${instance_name}" "Name=instance-state-name,Values=running" \
    --query "Reservations[0].Instances[0].{PrivateIpAddress:PrivateIpAddress,InstanceId:InstanceId,PublicIpAddress:PublicIpAddress,PublicDnsName:PublicDnsName}" \
    --output json)

  # Check if the AWS CLI command was successful and produced output
  if [[ -z "$instance_info" || "$instance_info" == "null" ]]; then
    echo "Error: No running instance found with the name '${instance_name}' or error fetching details." >&2
    return 2
  fi

  instance_id=$(echo "$instance_info" | jq -r .InstanceId)
  public_ip=$(echo "$instance_info" | jq -r .PublicIpAddress)
  public_dns_name=$(echo "$instance_info" | jq -r .PublicDnsName)
  private_ip=$(echo "$instance_info" | jq -r .PrivateIpAddress)


  if [[ "$instance_id" == "null" || "$instance_id" == "" ]]; then
    echo "Error: Could not extract InstanceId for a running instance with name '${instance_name}'." >&2
    return 3
  fi

  if [[ "$public_ip" != "null" && "$public_ip" != "" ]]; then
    connection_target="$public_ip"
  elif [[ "$public_dns_name" != "null" && "$public_dns_name" != "" ]]; then
    echo "Warning: Instance ${instance_id} does not have a public IP address. Using Public DNS Name: ${public_dns_name}" >&2
    connection_target="$public_dns_name"
  elif [[ "$private_ip" != "null" && "$private_ip" != "" ]]; then
    echo "Warning: Instance ${instance_id} does not have a public IP address or Public DNS Name. Using Private IP: ${private_ip}" >&2
    connection_target="$private_ip"
  else
    echo "Error: Instance ${instance_id} (Name: ${instance_name}) has neither a Public IP address nor a Public DNS name. Cannot determine connection target." >&2
    return 4
  fi

  # Return instance_id and connection_target space-separated
  echo "${instance_id} ${connection_target}"
  return 0
}

jh_output=$(get_instance_info "${JUMP_HOST}")
tg_output=$(get_instance_info "${TARGET_HOST}")
mn_output=$(get_instance_info "${MONITORING_HOST}")
read -r JH_INSTANCE_ID JH_CONNECTION_TARGET <<< "$jh_output"
read -r TG_INSTANCE_ID TG_CONNECTION_TARGET <<< "$tg_output"
read -r MN_INSTANCE_ID MN_CONNECTION_TARGET <<< "$mn_output"

echo "Found Jump Host Instance ID: ${JH_INSTANCE_ID}"
echo "Found Target Host Instance ID: ${TG_INSTANCE_ID}"
echo "Found Monitoring Host Instance ID: ${MN_INSTANCE_ID}"
echo "Will connect to: ${TG_CONNECTION_TARGET} via ${JH_CONNECTION_TARGET}"

echo "Retrieving SSH key from SSM Parameter Store: ${SSM_KEY_PARAMETER_NAME}"
if [ -f "${LOCAL_KEY_PATH}" ]; then
  echo "Local key file '${LOCAL_KEY_PATH}' already exists. Removing before replacement because it would cause a permission error."
  rm -f "${LOCAL_KEY_PATH}"
fi
aws ssm get-parameter \
  --name "${SSM_KEY_PARAMETER_NAME}" \
  --with-decryption \
  --query "Parameter.Value" \
  --output text > "${LOCAL_KEY_PATH}"

if [ $? -ne 0 ] || [ ! -s "${LOCAL_KEY_PATH}" ]; then
  echo "Error: Failed to retrieve SSH key from SSM Parameter Store or key is empty."
  # Attempt to clean up potentially empty key file
  [ -f "${LOCAL_KEY_PATH}" ] && rm -f "${LOCAL_KEY_PATH}"
  exit 1
fi
echo "SSH key retrieved and saved to ${LOCAL_KEY_PATH}"

chmod 400 "${LOCAL_KEY_PATH}"
echo "Permissions set to 400 for ${LOCAL_KEY_PATH}"

echo "Retrieving docdb host ..."
docdb_host=$(aws ssm get-parameter --name /docdb/ai/host --query 'Parameter.Value' --with-decryption --output text)
echo "DocDB host: ${docdb_host}"

echo "Attempting to SSH into ${TG_CONNECTION_TARGET} via ${JH_CONNECTION_TARGET} using key ${LOCAL_KEY_PATH}..."

echo "ssh -i ${LOCAL_KEY_PATH} ec2-user@${JH_CONNECTION_TARGET} -L 8265:${TG_CONNECTION_TARGET}:8265 -L 3000:${MN_CONNECTION_TARGET}:3000" -L "3333:${MN_CONNECTION_TARGET}:3333" -L "27017:${docdb_host}:27017"
ssh -i "${LOCAL_KEY_PATH}" "ec2-user@${JH_CONNECTION_TARGET}" -L "8265:${TG_CONNECTION_TARGET}:8265" -L "3000:${MN_CONNECTION_TARGET}:3000" -L "3333:${MN_CONNECTION_TARGET}:3333" -L "27017:${docdb_host}:27017"
#ssh -fN -L 8265:${TG_CONNECTION_TARGET}:8265 \
 #   -i "$HOME/.ssh/ray_jump_host.pem" \
  #  ec2-user@${JH_CONNECTION_TARGET}
# Optional: Cleanup the local key file

ssh -fN -L 8265:${TG_CONNECTION_TARGET}:8265 \
    -i "$HOME/.ssh/ray_jump_host.pem" \
    ec2-user@${JH_CONNECTION_TARGET}
read -p "Do you want to remove the local SSH key file (${LOCAL_KEY_PATH})? (y/N): " confirm
if [[ "$confirm" == [yY] || "$confirm" == [yY][eE][sS] ]]; then
  rm -f "${LOCAL_KEY_PATH}"
  echo "Local SSH key file ${LOCAL_KEY_PATH} removed."
else
  echo "Local SSH key file ${LOCAL_KEY_PATH} was not removed."
fi

echo "Script finished."