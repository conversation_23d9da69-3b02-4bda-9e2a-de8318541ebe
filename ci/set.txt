
case "$Environment" in
  "AI-Integration")
    export AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID_AI_INTEGRATION}"
    export AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY_AI_INTEGRATION}"
    ;;
  "CUJU-AI-Integration")
    export AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID_CUJU_AI_INTEGRATION}"
    export AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY_CUJU_AI_INTEGRATION}"
    ;;  
  "CUJU-AI-Test")
    export AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID_CUJU_AI_TEST}"
    export AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY_CUJU_AI_TEST}"
    ;; 
  "CUJU-AI-Production")
    export AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID_CUJU_AI_PRODUCTION}"
    export AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY_CUJU_AI_PRODUCTION}"
    ;; 
  *)
    echo "Unknown environment: $Environment"
    echo "Please use one of the following environments: AI-Integration, CUJU-AI-Integration, CUJU-AI-Test, CUJU-AI-Production"
    return 1
    ;;
esac

{
  echo "ServerAliveInterval 180"
  echo "Host $JUMPHOST_IP"
  echo "    StrictHostKeyChecking no"
  echo "    UserKnownHostsFile /dev/null"
} > ~/.ssh/config

cp ./id_rsa ~/.ssh/${SSH_KEY_NAME}.pem
echo "Environment is set to $Environment"
