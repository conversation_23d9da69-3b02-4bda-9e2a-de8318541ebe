FROM rayproject/ray:2.42.1-py312-gpu

WORKDIR /app

RUN sudo apt-get update && sudo apt-get install -y \
    build-essential \
    rsync \
    curl \
    git \
    ffmpeg \
    libsm6 \
    libxext6 \
    cudnn9-cuda-12 \
    && sudo rm -rf /var/lib/apt/lists/*

RUN curl -sSL https://install.python-poetry.org | python3 -

ENV POETRY_NO_INTERACTION=1 \
    PATH="/home/<USER>/.local/bin:$PATH" \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# --- AWS CLI + Prometheus ---
RUN pip install "awscli==1.37.17" "botocore==1.36.17" "s3transfer>=0.11.0,<0.12.0"

RUN wget https://github.com/prometheus/prometheus/releases/download/v2.51.2/prometheus-2.51.2.linux-amd64.tar.gz \
    && tar -xzf prometheus-2.51.2.linux-amd64.tar.gz \
    && sudo mv prometheus-2.51.2.linux-amd64/prometheus /usr/local/bin/ \
    && rm -rf prometheus-2.51.2.linux-amd64* \
    && sudo apt-get clean

# --- Arguments for conditional dependency install ---
ARG INSTALL_CODEARTIFACT_DEPS=false
ARG ENVIRONMENT=production
ARG CODEARTIFACT_DOMAIN=cuju-ai
ARG CODEARTIFACT_DOMAIN_OWNER=651706744076
ARG CODEARTIFACT_REPOSITORY=cuju-ai-workbench
ARG AWS_DEFAULT_REGION=eu-central-1

# --- Copy correct pyproject.toml ---
COPY ./configs/${ENVIRONMENT}/app_configs/pyproject.toml /app/pyproject.toml

# --- Conditional CodeArtifact + Poetry install ---
RUN --mount=type=secret,id=aws_codeartifact_token \
    if [ "$INSTALL_CODEARTIFACT_DEPS" = "true" ]; then \
      CODEARTIFACT_AUTH_TOKEN=$(sudo cat /run/secrets/aws_codeartifact_token) && \
      poetry config repositories.aws "https://${CODEARTIFACT_DOMAIN}-${CODEARTIFACT_DOMAIN_OWNER}.d.codeartifact.${AWS_DEFAULT_REGION}.amazonaws.com/pypi/${CODEARTIFACT_REPOSITORY}/simple" && \
      poetry config http-basic.aws aws "$CODEARTIFACT_AUTH_TOKEN" && \
      poetry config virtualenvs.create false && \
      poetry install --no-root; \
    else \
      echo "Skipping dependency installation."; \
    fi

# --- Replace onnxruntime with onnxruntime-gpu ---
RUN pip uninstall --yes onnxruntime && \
    pip install onnxruntime-gpu --index-url "https://pypi.org/simple" --quiet