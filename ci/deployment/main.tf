provider "aws" {
  region = "eu-central-1"
}

data "aws_ssm_parameter" "private_subnet_1_id" {
  name            = "/ray-clusters/private_subnet_1_id"
  with_decryption = true
}

data "aws_ssm_parameter" "private_subnet_2_id" {
  name            = "/ray-clusters/private_subnet_2_id"
  with_decryption = true
}

data "aws_ssm_parameter" "private_subnet_3_id" {
  name            = "/ray-clusters/private_subnet_3_id"
  with_decryption = true
}

data "aws_ssm_parameter" "iam_ray_cluster_arn" {
  name            = "/ray-clusters/iam_ray_cluster_arn"
  with_decryption = true
}

data "aws_ssm_parameter" "ray_cluster_sg_id" {
  name            = "/ray-clusters/ray_cluster_sg_id"
  with_decryption = true
}

data "aws_ssm_parameter" "jumphost_ray_cluster_ip" {
  name            = "/ray-clusters/jumphost_ray_cluster_ip"
  with_decryption = true
}

data "aws_ssm_parameter" "jumphost_ray_cluster_ssh_key_name" {
  name            = "/ray-clusters/jumphost_ray_cluster_ssh_key"
  with_decryption = true
}

data "aws_ssm_parameter" "jumphost_ray_cluster_ssh_key" {
  name            = "/ray-clusters/jumphost_ssh_key"
  with_decryption = true
}

resource "local_file" "ssh_key_file" {
  content         = data.aws_ssm_parameter.jumphost_ray_cluster_ssh_key.value
  filename        = "../../id_rsa"
  file_permission = "0600"
}


data "aws_ssm_parameter" "account_id" {
  name            = "account_id"
  with_decryption = true
}

data "aws_ssm_parameter" "shared_account_id" {
  name            = "shared_account_id"
  with_decryption = true
}
data "aws_ssm_parameter" "monitoring_instance" {
  name            = "/ray-clusters/monitoring_private_ip"
  with_decryption = true
}


resource "local_file" "export_script" {
  filename = "set_env_terraform.sh"
  content  = <<EOT
#!/bin/sh

export Environment="${var.environment}"

export AWS_REGION="eu-central-1"
export SHARED_ACCOUNT_ID="************"

export SUBNET_1_ID=${data.aws_ssm_parameter.private_subnet_1_id.value}
export SUBNET_2_ID=${data.aws_ssm_parameter.private_subnet_2_id.value}
export SUBNET_3_ID=${data.aws_ssm_parameter.private_subnet_3_id.value}
export IAM_RAY_CLUSTERS_ROLE_ARN=${data.aws_ssm_parameter.iam_ray_cluster_arn.value}
export RAY_CLUSTER_SG_ID=${data.aws_ssm_parameter.ray_cluster_sg_id.value}
export JUMPHOST_IP=${data.aws_ssm_parameter.jumphost_ray_cluster_ip.value}
export SSH_KEY_NAME=${data.aws_ssm_parameter.jumphost_ray_cluster_ssh_key_name.value}
export ACCOUNT_ID=${data.aws_ssm_parameter.account_id.value}
export SHARED_ACCOUNT_ID=${data.aws_ssm_parameter.shared_account_id.value}
export MONITORING_INSTANCE_IP=${data.aws_ssm_parameter.monitoring_instance.value}

EOT
}
