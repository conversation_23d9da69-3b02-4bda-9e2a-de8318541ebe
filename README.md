# Cuju AI Inference Server

[![Python](https://img.shields.io/badge/python-3670A0?logo=python&logoColor=ffdd54)](https://www.python.org/)
[![Poetry](https://img.shields.io/endpoint?url=https://python-poetry.org/badge/v0.json)](https://python-poetry.org/)
[![Ruff](https://img.shields.io/endpoint?url=https://raw.githubusercontent.com/astral-sh/ruff/main/assets/badge/v2.json)](https://github.com/astral-sh/ruff)

A service for making inference with Ray

## Code Navigation

```bash
.
├── bitbucket-pipelines.yml
├── ci
    ├── build_and_push_image.sh
    ├── set_env.sh
    └── teardown-ray-clusters.sh
├── cluster
    ├── cluster-config.j2
    ├── cluster_gpu.yaml
    └── render_cluster_config_template.py
├── cuju-ai-inference-server
    ├── app
        ├── check_gpu.py
        ├── params_*.toml
        ├── pipe_*.yaml
        ├── pyproject.toml
        └── ray_config_*.yaml
    ├── Dockerfile.cpu-aws
    └── Dockerfile.gpu-aws
└── tests
    └── __init__.py
```

## Setup

The following setup instructions target Unix systems. Where feasible, documents for platform independent installation steps are linked but most scripts and commands are tailored to BASH-like shells. If you develop under Windows, we recommend [installing and using WSL](https://learn.microsoft.com/windows/wsl/install), [DevContainers](https://code.visualstudio.com/docs/devcontainers/containers) or a [Linux VM](https://www.virtualbox.org/).

## Prerequisites

- Request and [configure](https://support.atlassian.com/bitbucket-cloud/docs/set-up-personal-ssh-keys-on-linux/) access to [our Bitbucket Repo](https://bitbucket.org/rogontechnologies/cuju-ai-video-stabilizer)
- Request access to our AI account users on the [AWS Portal](https://rogontech-sso.awsapps.com/start/#/) (for downloading models, accessing S3 data etc.)
- (Optional) [Install Pyenv](https://github.com/pyenv/pyenv?tab=readme-ov-file#getting-pyenv) for managing different Python versions
- [Install Python](https://github.com/pyenv/pyenv?tab=readme-ov-file#install-additional-python-versions) **3.9** (currently most stable and tested)
- [Install Poetry](https://python-poetry.org/docs/#installing-with-the-official-installer) and add it to your PATH
- (Windows Users) [Install Python3.9 dev package] by executing `pip install libpython3.9-dev`.
- Install **[Pycharm](https://www.jetbrains.com/pycharm/)** (or [VS Code](https://code.visualstudio.com/), if no Pycharm License is available)

## Installation

Clone the repository (make sure to [setup SSH authentication](https://support.atlassian.com/bitbucket-cloud/docs/set-up-personal-ssh-keys-on-linux/) first):
  `<NAME_EMAIL>:rogontechnologies/cuju-ai-inference-server.git`

## Local Deployment

> **NOTE**  
See `AWS Shared Access Key` within the KeePass store in `/doc/secrets/ci_cd.kdbx` for AWS CodeArtifactory Credentials

In the service repository, we merely consume the pip packages from the AWS CodeArtifact.
The `pyproject.toml` defines which packages we require and the `main_ray.py`  defines how the service is build.

Once the desired packages are available from AWS, they can be deployed anywhere with the following commands:

1. Authenticate with AWS and generate an auth-token:

   ```bash
   aws codeartifact login --tool pip --domain $DOMAIN --domain-owner $DOMAIN_OWNER --repository $REPOSITORY --region $REGION
   aws codeartifact get-authorization-token --domain $DOMAIN --domain-owner $DOMAIN_OWNER --region $REGION --query authorizationToken --output text > .code-artifact-token
   ```

2. Tell Poetry to use the auth-token to access the registry and remove the token file:

   ```bash
   poetry config repositories.aws "https://${DOMAIN}-${DOMAIN_OWNER}.d.codeartifact.${REGION}.amazonaws.com/pypi/${REPOSITORY}/simple/"   
   poetry config http-basic.aws aws $(cat .code-artifact-token)   
   rm .code-artifact-token
   ```

3. Install the service dependencies by running:

    ```bash
    cd cuju-ai-inference-server/app/pyproject.toml
    poetry install
    ```

4. Spin up the ray cluster

    ```bash
    poetry run ray start --head
    ```

5. Deploy application to the ray cluster (single node only)

    ```bash
    poetry run ray serve ray_config.yaml
    ```

> **NOTE:** Don't run `aws codeartifact login --tool pip --repository $REPO --domain $DOMAIN --domain-owner $DOMAIN_OWNER --region $REGION` as stated in some AWS docs.
> This could break your pip install. If you accidentally set up pip with a global index (check if `pip config list` yields a `global.index-url`) then you need to remove it with `pip config unset global.index-url` to avoid side-effects

## Cloud Deployment
ssz
Within the bitbucket pipeline (`bitbucket-pipelines.yml`), the steps are defined to deploy the service to the cloud.

1. Get secrets to authenticate against the necessary AWS resources
2. Build and push docker images (Ray base image)
3. Create Ray SSH Key, so the pipeline can access the cluster head nodes.
4. Render cluster templates (for cluster 1, 2, 3)
5. Deploy clusters in parallel, while the pipeline runs `ray up cluster_config.yaml`
6. Deploy application with `ray exec cluster_config.yaml 'serve deploy ray_config.yaml'`

Once the pipline successfully deployed the ray clusters and the ray applications, we can interact with the cluster.

### Cloud Deployment Prerequisites

1. We have to get the cluster config file.
   2. either you re-render it again with the template file
   3. or you download the cluster_config artifact from the pipeline run
2. Configure the aws credential to reflect the `AI-Integration-ray` user
3. We have to locally store the `ray-autoscaler_eu-central-1.pem` file in the local
   `~/.ssh` directory

### Useful Commands

- To access dashboards:

   ```bash
   poetry run ray dashboard {cluster.yaml}
   ```

- To attach to the cluster:

   ```bash
   poetry run ray attach {cluster.yaml}
   ```

- To deploy a new application / model:

   ```bash
   poetry run ray exec {cluster.yaml} 'serve deploy {ray_config.yaml}'
   ```

- To check applications statuses:

   ```bash
   poetry run ray exec {cluster.yaml} 'serve status'
   ```

- To see the current application config(s):

   ```bash
   poetry run ray exec {cluster.yaml} 'serve config'
   ```

## Documentation

- [Project Documentation](https://cuju.atlassian.net/wiki/spaces/C2)
- [Legacy Documentation](https://cuju.atlassian.net/wiki/spaces/ROGON)
- [Contributing: Coding Guidelines](https://cuju.atlassian.net/wiki/spaces/ROGON/pages/11376644/Coding+Guidelines)
